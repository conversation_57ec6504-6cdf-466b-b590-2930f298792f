<template>
  <div id="tag-manage-wrapper">
    <div class="btn-box">
      <div class="expand-collapse">
        <a-button @click="expandAll">全部展开</a-button>
        <a-button type="dashed" @click="collapseAll">全部收起</a-button>
      </div>
      <div>
        <a-tree-select
          v-if="treeData.length"
          v-model:value="treeValue"
          show-search
          style="margin-right: 15px; min-width: 200px"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择部门"
          allow-clear
          tree-default-expand-all
          :tree-data="treeData"
          :fieldNames="{ label: 'departName', value: 'orgCode' }"
          @change="handleTreeChange"
        >
        </a-tree-select>
        <a-button type="primary" @click="addTag" v-if="isMyPart">新建标签</a-button>
      </div>
    </div>

    <a-table
      :columns="columns"
      :dataSource="data"
      rowKey="id"
      :pagination="false"
      :indentSize="10"
      :expandedRowKeys="expandedKeys"
      @expand="onExpand"
    >
      <template #headerCell="{ title, column }">
        <template v-if="column.key === 'markCount'">
          <div style="display: flex; align-items: center; gap: 7px">
            <span>{{ title }}</span>
            <Tooltip title="每增加1个标注框即计数1，1张照片中允许出现多个同一标签的标注框。">
              <InfoCircleOutlined />
            </Tooltip>
          </div>
        </template>
      </template>

      <template #bodyCell="{ record, column }" v-if="isMyPart">
        <template v-if="column.dataIndex === 'operation'">
          <a-button type="link" @click="editRow(record)">编辑</a-button>
          <a-divider type="vertical" />
          <a-popconfirm title="确定删除该分类/标签吗？" ok-text="确定" cancel-text="取消" @confirm="deleteRow(record)">
            <a-button type="link">删除</a-button>
          </a-popconfirm>
        </template>
        <template v-if="column.dataIndex === 'markCount'">
          <a-button type="link" @click="handleMarkCount(record)">{{ record.markCount }}</a-button>
        </template>
      </template>
    </a-table>

    <a-modal
      :title="editRowId ? '编辑' : '新建'"
      v-model:visible="isModalVisible"
      destroyOnClose
      @ok="handleOk"
      @cancel="handleCancel"
      :getContainer="getContainer"
    >
      <div v-if="editRowId === ''">
        <a-form :model="formValues" ref="formRef">
          <a-form-item
            name="createType"
            label="创建方式"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
            :rules="{ required: true, message: '请选择创建方式' }"
          >
            <a-select placeholder="请选择创建方式" v-model:value="formValues.createType" @change="(value) => changeCreateType(value)">
              <a-select-option value="1">独立创建</a-select-option>
              <a-select-option value="2">上级获取</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            name="myDepartment"
            label="归属部门"
            :label-col="{ span: 4 }"
            :wrapper-col="{ span: 20 }"
            :rules="{ required: true, message: '请选择归属部门' }"
          >
            <a-select
              placeholder="请选择归属部门"
              v-model:value="formValues.myDepartment"
              :options="departmentOptions"
              :fieldNames="{ label: 'departName', value: 'orgCode' }"
              @change="getParentList()"
            >
            </a-select>
          </a-form-item>
        </a-form>
        <a-divider />
      </div>
      <a-tabs v-model:activeKey="activeTab" v-if="formValues.createType === '1'">
        <a-tab-pane key="1" :tab="editRowId ? '编辑标签类' : '新建标签类'" :disabled="editRowId !== '' && activeTab === '2'">
          <a-form :model="formValues1" ref="formRef1">
            <a-form-item
              name="name"
              label="标签类名称"
              :label-col="{ span: 5 }"
              :wrapper-col="{ span: 19 }"
              :rules="[{ required: true, message: '请输入标签类名称', whitespace: true }]"
            >
              <a-input v-model:value="formValues1.name" :maxlength="20" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
        <a-tab-pane key="2" :tab="editRowId ? '编辑标签' : '新建标签'" :disabled="editRowId !== '' && activeTab === '1'">
          <a-form :model="formValues2" ref="formRef2">
            <a-form-item
              name="parentId"
              label="标签类型"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              :rules="[{ required: true, message: '请选择标签类型' }]"
            >
              <a-select v-model:value="formValues2.parentId" :options="parentOptions" />
            </a-form-item>
            <a-form-item
              name="name"
              label="标签名称"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              :rules="[{ required: true, message: '请输入标签名称', whitespace: true }]"
            >
              <a-input v-model:value="formValues2.name" :maxlength="20" />
            </a-form-item>
            <a-form-item
              name="causeDescription"
              label="成因分析"
              :label-col="{ span: 4 }"
              :wrapper-col="{ span: 20 }"
              :rules="[{ message: '请输入标签成因分析', whitespace: true }]"
            >
              <a-textarea v-model:value="formValues2.causeDescription" showCount :maxlength="300" :rows="4" />
            </a-form-item>
            <a-form-item
              name="adviseDescription"
              label="养护/维修建议"
              :label-col="labelCol"
              :wrapper-col="wrapperCol"
              :rules="[{ message: '请输入标签养护/维修建议', whitespace: true }]"
            >
              <a-textarea v-model:value="formValues2.adviseDescription" showCount :maxlength="300" :rows="4" />
            </a-form-item>
          </a-form>
        </a-tab-pane>
      </a-tabs>
      <div style="margin-top: 30px" v-else>
        <div class="tree-container" v-if="parentTreeData.length">
          <div class="tree-header">
            <a-checkbox v-model:checked="checkAll" :indeterminate="indeterminate" @change="onCheckAllChange"> 全选 </a-checkbox>
          </div>
          <div class="tree-content">
            <a-tree
              v-model:checkedKeys="parentLabelIds"
              :fieldNames="{ title: 'name', key: 'id' }"
              :tree-data="parentTreeData"
              checkable
              :selectable="false"
              @check="onTreeCheck"
            />
          </div>
        </div>
        <div v-else>暂无数据</div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts" name="dataManage-tagManage" setup>
import { ref, computed, onMounted, reactive, watch, toRefs } from 'vue';
import { message } from 'ant-design-vue';
import { tagManageApi } from './tagManage.api';
import { useRouter } from 'vue-router';
import { InfoCircleOutlined } from '@ant-design/icons-vue';
import { Tooltip } from 'ant-design-vue';
import { ellipse } from '@turf/turf';
import { getDepartNameByCode } from '/@/utils/common/compUtils';

const isMyPart = ref(false);

const router = useRouter();

const columns = [
  {
    title: '分类/标签名称',
    dataIndex: 'name',
    key: 'name',
  },
  {
    title: '标记数量',
    dataIndex: 'markCount',
    key: 'markCount',
    sorter: (a, b) => a.markCount - b.markCount,
  },
  {
    title: '所属部门',
    dataIndex: 'myDepartName',
    key: 'myDepartName',
    customRender: function ({ text, record }) {
      const { sysOrgCode } = record as any;
      return getDepartNameByCode(sysOrgCode);
    },
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: (a, b) => new Date(a.createTime) - new Date(b.createTime),
  },
  {
    title: '成因分析',
    dataIndex: 'causeDescription',
    key: 'causeDescription',
    ellipsis: true,
  },
  {
    title: '养护/维修建议',
    dataIndex: 'adviseDescription',
    key: 'adviseDescription',
    ellipsis: true,
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
  },
];
const treeValue = ref<string>();

const treeData = ref([]);
const parentTreeData = ref([]);
const data = ref([]);

const labelCol = ref({
  span: 4,
  style: 'line-height: 16px; white-space: normal;text-align: left',
});
const wrapperCol = ref({
  span: 20,
});

const isModalVisible = ref(false);
const formValues = ref({ myDepartment: undefined, createType: '1' });

const formValues1 = ref({ name: '' });
const formValues2 = ref({ parentId: null, name: '' });
const activeTab = ref('1');

const expandedKeys = ref([]);
const formRef = ref(null);
const formRef1 = ref(null);
const formRef2 = ref(null);
const editRowId = ref('');

const departmentOptions = ref([]);

// 选中节点的key数组
const checkedKeys = ref([]);
// 全选状态
const checkAll = ref(false);
// 半选状态
const indeterminate = ref(false);

const parentLabelIds = ref([]);
const parentOptions = computed(() => {
  return data.value.map((item) => ({ label: item.name, value: item.id }));
});
// 获取所有节点的key（包括子节点）
const getAllKeys = (nodes) => {
  let keys = [];
  // 添加安全检查，确保nodes存在且为数组
  if (!nodes || !Array.isArray(nodes)) {
    return keys;
  }
  nodes.forEach((node) => {
    keys.push(node.id);
    if (node.children && node.children.length > 0) {
      keys = keys.concat(getAllKeys(node.children));
    }
  });
  return keys;
};

// 全选复选框变化事件
const onCheckAllChange = (e) => {
  if (e.target.checked) {
    // 选中所有节点
    parentLabelIds.value = getAllKeys(parentTreeData.value);
  } else {
    // 清空所有选中
    parentLabelIds.value = [];
  }
  indeterminate.value = false;
};

// 树节点选中事件
const onTreeCheck = () => {
  const allKeys = getAllKeys(parentTreeData.value);
  const checkedCount = parentLabelIds.value.length;

  // 更新全选和半选状态
  checkAll.value = checkedCount === allKeys.length;
  indeterminate.value = checkedCount > 0 && checkedCount < allKeys.length;
};

// 监听选中键变化
watch(parentLabelIds, () => {
  const allKeys = getAllKeys(parentTreeData.value);
  const checkedCount = parentLabelIds.value.length;

  checkAll.value = checkedCount === allKeys.length;
  indeterminate.value = checkedCount > 0 && checkedCount < allKeys.length;
});
watch(treeValue, () => {
  console.log('select', treeValue.value);
});
const getContainer = () => {
  return document.getElementById('tag-manage-wrapper');
};
const transformTreeData = (data) => {
  return data.map((node) => {
    const newNode = {
      ...node,
      disabled: node.disableCheckbox,
      label: node.departName,
      value: node.orgCode,
      key: node.orgCode,
    };
    delete newNode.disableCheckbox;

    if (node.children) {
      newNode.children = transformTreeData(node.children);
    }

    return newNode;
  });
};
const getDepartmentList = () => {
  // 获取部门及字部门树
  const treeObj = localStorage.getItem('myDepartAndChildrenTree');
  const rawData = treeObj ? JSON.parse(treeObj) : [];
  treeData.value = transformTreeData(rawData);

  // 获取所属部门
  const myDepartList = localStorage.getItem('myDepartList');

  treeValue.value = myDepartList ? JSON.parse(myDepartList)[0].orgCode : '';

  const parsedDepartList = myDepartList ? JSON.parse(myDepartList) : [];
  isMyPart.value = parsedDepartList.some((item) => item.orgCode === treeValue.value);

  //获取负责的部门列表
  const chargeDepartment = localStorage.getItem('myResponsibleDepartList');
  departmentOptions.value = chargeDepartment ? JSON.parse(chargeDepartment) : [];

  //归属部门默认选中负责部门第一条数据
  if (departmentOptions.value && departmentOptions.value.length > 0) {
    formValues.value.myDepartment = departmentOptions.value[0].orgCode;
    console.log('formValues.value.myDepartment', formValues.value.myDepartment);
  }
};

const handleEdit = (record) => {};

const addTag = () => {
  formValues.value = { myDepartment: departmentOptions.value.length ? departmentOptions.value[0].orgCode : undefined, createType: '1' };

  parentLabelIds.value = [];
  checkAll.value = false;
  indeterminate.value = false;
  activeTab.value = '1';
  editRowId.value = '';

  isModalVisible.value = true;
  formValues1.value = { name: '' };
  formValues2.value = { parentId: null, name: '' };
  // 重置表单状态
  formRef.value?.resetFields();
  formRef1.value?.resetFields();
  formRef2.value?.resetFields();
};

const getParentList = () => {
  tagManageApi.getParentList({ sysOrgCode: formValues.value.myDepartment }).then((res) => {
    parentTreeData.value = res || [];
  });
};

const changeCreateType = (value) => {
  if (value === '2') {
    //从上级获取标签列表添加标签
    // 确保formValues.value.myDepartment存在再调用API
    if (formValues.value && formValues.value.myDepartment) {
      getParentList();
    }
  } else {
    //独立创建标签，复用旧逻辑
  }
};
// 过滤选中节点，并统一替换 sysOrgCode
const buildSelectedTree = (tree, checkedKeys, sysOrgCode) => {
  return tree
    .map((node) => {
      if (checkedKeys.includes(node.id)) {
        // 当前节点选中 → 保留，替换 sysOrgCode
        return {
          ...node,
          sysOrgCode,
          children: node.children ? buildSelectedTree(node.children, checkedKeys, sysOrgCode) : [],
        };
      } else if (node.children && node.children.length > 0) {
        // 子节点可能被选中 → 递归过滤
        const filteredChildren = buildSelectedTree(node.children, checkedKeys, sysOrgCode);
        if (filteredChildren.length > 0) {
          return {
            ...node,
            sysOrgCode,
            children: filteredChildren,
          };
        }
      }
      return null;
    })
    .filter(Boolean);
};
const handleOk = async () => {
  // 从上级获取标签列表添加标签
  if (formValues.value.createType === '2') {
    if (parentLabelIds.value.length === 0) {
      message.error('请选择标签！');
      return;
    } else {
      const selectedTree = buildSelectedTree(parentTreeData.value, parentLabelIds.value, formValues.value.myDepartment);
      tagManageApi.addBatch(selectedTree).then((res) => {
        // parentTreeData.value =
        isModalVisible.value = false;
        parentLabelIds.value = [];
        checkAll.value = false;
        indeterminate.value = false;
        getList();
      });
    }
  } else {
    // 当前所在tab页
    if (activeTab.value === '1') {
      formRef1.value?.validateFields().then(() => {
        if (editRowId.value) {
          // 编辑
          const params = {
            id: editRowId.value,
            sysOrgCode: formValues.value.myDepartment,
            ...formValues1.value,
          };
          tagManageApi.edit(params).then((res) => {
            isModalVisible.value = false;
            getList();
          });
        } else {
          // 新增
          tagManageApi.add({ ...formValues1.value, sysOrgCode: formValues.value.myDepartment }).then((res) => {
            isModalVisible.value = false;
            getList();
          });
        }
      });
    } else if (activeTab.value === '2') {
      formRef2.value?.validateFields().then(() => {
        if (editRowId.value) {
          // 编辑
          const params = {
            id: editRowId.value,
            sysOrgCode: formValues.value.myDepartment,
            ...formValues2.value,
          };
          tagManageApi.edit(params).then((res) => {
            isModalVisible.value = false;
            getList();
          });
        } else {
          // 新增
          tagManageApi.add({ ...formValues2.value, sysOrgCode: formValues.value.myDepartment }).then((res) => {
            isModalVisible.value = false;
            getList();
          });
        }
      });
    }
  }
};

const handleCancel = () => {
  isModalVisible.value = false;
};

const editRow = (record) => {
  // 当前编辑项的id
  editRowId.value = record.id;
  if (record.parentId) {
    // 二级标签
    activeTab.value = '2';
    formValues2.value = {
      parentId: record.parentId,
      name: record.name,
      adviseDescription: record.adviseDescription,
      causeDescription: record.causeDescription,
    };
  } else {
    // 一级标签
    activeTab.value = '1';
    formValues1.value = { name: record.name };
  }
  isModalVisible.value = true;
};

const deleteRow = (record) => {
  if (record.markCount > 0) {
    message.error('该标签下有内容，无法删除');
  } else {
    tagManageApi.delete(record.id).then((res) => {
      getList();
    });
  }
};

const expandAll = () => {
  expandedKeys.value = [];
  const addKeys = (nodes) => {
    nodes.forEach((node) => {
      expandedKeys.value.push(node.id);
      if (node.children) {
        addKeys(node.children);
      }
    });
  };
  addKeys(data.value);
};

const collapseAll = () => {
  expandedKeys.value = [];
};

const onExpand = (expanded, record) => {
  if (expanded) {
    expandedKeys.value.push(record.id);
  } else {
    expandedKeys.value = expandedKeys.value.filter((key) => key !== record.id);
  }
};

const handleMarkCount = (record) => {
  console.log('record', record);
  // if (record.markCount === 0) {
  //   return;
  // }
  const labelIds = [];
  if (record.children) {
    record.children.forEach((item) => {
      labelIds.push(item.id);
    });
  } else {
    labelIds.push(record.id);
  }
  console.log('labelIds', labelIds);
  router.push({
    path: '/dataManage/dataManage',
    query: { labelIds: JSON.stringify(labelIds), sysOrgCode: treeValue.value },
  });
};
const getList = (params = {}) => {
  const obj = { ...params, sysOrgCode: treeValue.value };
  tagManageApi.getList(obj).then((res) => {
    console.log('res=>>', res);
    data.value = res;
  });
};
const handleTreeChange = () => {
  const myDepartList = localStorage.getItem('myDepartList');

  const parsedDepartList = myDepartList ? JSON.parse(myDepartList) : [];

  isMyPart.value = parsedDepartList.some((item) => item.orgCode === treeValue.value);
  getList();
};
onMounted(() => {
  getContainer();
  getDepartmentList();

  getList();
});
</script>

<style lang="less" scoped>
#tag-manage-wrapper {
  margin: 20px;
  padding: 10px;
  background-color: #fff;
  height: calc(100% - 40px);
  .btn-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .expand-collapse {
      button {
        margin-right: 6px;
      }
    }
  }
  :deep(.ant-modal .ant-modal-body) {
    padding: 20px 20px 20px 20px;
  }
  .tree-container {
    .tree-header {
      padding: 8px;
    }

    .tree-content {
      max-height: 300px;
      overflow-y: auto;
    }
  }
}
:deep(.ant-btn-link) {
  padding: 0;
}
</style>
