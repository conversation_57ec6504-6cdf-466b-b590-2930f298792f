<template>
  <div class="p-4" ref="modelModal">
    <div class="tableWrap">
      <div class="department-tree">
        <a-tree
          v-if="treeData.length"
          v-model:selectedKeys="selectedKeys"
          v-model:checkedKeys="checkedKeys"
          defaultExpandAll
          @select="onSelect"
          :tree-data="treeData"
          :fieldNames="{ title: 'departName', key: 'orgCode' }"
        >
        </a-tree>
      </div>
      <BasicTable
        ref="tableRef"
        @register="registerTable"
        :searchInfo="queryParam"
        @change="tableChange"
        :rowSelection="rowSelection"
        :scroll="{ x: 'max-content' }"
        tableLayout="fixed"
      >
        <template #bodyCell="{ record, column }">
          <template v-if="column.dataIndex === 'fileName'">
            <div class="colFileName">
              <!--名称(文件夹)-->
              <template v-if="record.isDir">
                <a-space>
                  <template v-if="record.directoryType === '3'">
                    <div class="dirIcon"><img :src="jobImgUrl('文件夹-黄', 'svg')" /></div>
                  </template>

                  <template v-else>
                    <div class="dirIcon"><img :src="jobImgUrl('文件夹-蓝', 'svg')" /></div>
                  </template>

                  <template v-if="editableData.directoryName && editableData.dirId === record.dirId">
                    <a-input
                      ref="input_focus"
                      v-model:value="editableData.directoryName"
                      style="margin: -5px 0"
                      @pressEnter="handleEnter"
                      @blur="handleEnter"
                    />
                  </template>

                  <template v-else>
                    <Tooltip :title="record.directoryName" placement="topLeft">
                      <span @click="openDir(record)" class="openDir">{{ record.directoryName }}</span>
                    </Tooltip>
                  </template>
                </a-space>
              </template>
              <!--名称(文件)-->

              <template v-else>
                <div class="fileInfoWrap">
                  <template v-if="record.thumbnailUrl">
                    <div class="fileIcon"><img :src="record.thumbnailUrl" onerror="this.style.display='none'" /></div>
                  </template>

                  <template v-if="editableData.fileName && editableData.fileId === record.fileId">
                    <a-input
                      ref="input_focus"
                      v-model:value="editableData.fileNameNoSuffix"
                      style="margin: -5px 0"
                      @pressEnter="handleEnter"
                      @blur="handleEnter"
                    />
                  </template>

                  <template v-else>
                    <Tooltip :title="record.fileName" placement="topLeft">
                      <span @click="visibleModal('openFile', record)" class="openDir">{{ record.fileName }}</span>
                    </Tooltip>
                    <!-- <span @click="visibleModal('openFile', record)" class="openDir">{{ record.fileName }} </span> -->
                  </template>
                </div>
              </template>
            </div>
          </template>

          <template v-if="column.dataIndex === 'fileType'">
            <div>
              {{ optionsList.fileType.find((item) => item.value && item.value === record.fileType)?.label || '' }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'labelStatus'">
            <div :class="labelStatusObj(record).cls">{{ labelStatusObj(record).txt }}</div>
          </template>
          <template v-if="column.dataIndex === 'myDepartName'">
            <div>{{ getDepartNameByCode(record.sysOrgCode) }}</div>
          </template>
          <template v-if="column.dataIndex === 'showMap'">
            <div>{{ showMapStatus[record.showMap] }}</div>
          </template>
          <template v-if="column.dataIndex === 'modelingStatus'">
            <div v-if="[null, 0].includes(record.mediaUploadStatus)"> -- </div>
            <div v-else>
              <div class="resultStatus"> <a-badge :status="resultIcon('icon', record)" />{{ resultIcon('resultStatus', record) }} </div>
              <div class="resultNum">{{ `(${record.mediaSuccessCount || '0'}/${record.mediaCount || '0'})` }}</div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'modelingProgress'">
            <div class="progress-result">
              <span class="result-text">{{ progressText(record) }}</span>
              <Tooltip :title="record.modelingRemark" v-if="record.modelingStatus === 40">
                <InfoCircleOutlined />
              </Tooltip>
            </div>
            <Progress v-if="record.modelingProgress" size="small" :percent="record.modelingProgress" :status="progressStatus(record)" />
          </template>
          <template v-if="column.dataIndex === 'shrinkProgress'">
            <div class="result-text" v-if="record.exportTaskInfo?.status === '40'">{{ record.exportTaskInfo?.errorMessage }}</div>
            <Progress
              v-if="record.exportTaskInfo?.progress"
              size="small"
              :percent="record.exportTaskInfo?.progress"
              :status="shrinkProgressStatus(record)"
            />
          </template>

          <template v-if="column.dataIndex === 'action'">
            <TableAction :actions="createActions(record, column)">
              <!-- <a-tooltip placement="top">
              <div
                class="bg"
                style="color: #40a9ff; cursor: pointer"
                @click="winOpen"
                v-if="record.fileDirectoryShareInfo?.status === 0 && record.fileDirectoryShareInfo?.shareUrl"
              >
                查看报告</div
              >
              <template #title>
                <div style="width: 150px; height: 150px">
                  <img style="width: 100%; height: 100%" :src="IMG" alt="" />
                </div>
              </template>
            </a-tooltip> -->
              <template #qrcode="{ row }">
                <Tooltip placement="top">
                  <div class="bg" style="color: #1890ff; cursor: pointer"> <QrcodeOutlined />{{ row.label }}</div>
                  <template #title>
                    <div style="width: 150px; height: 150px">
                      <img style="width: 100%; height: 100%" :src="record.fileDirectoryShareInfo.toQrcode" alt="" />
                    </div>
                  </template>
                </Tooltip>
              </template>
            </TableAction>
          </template>
        </template>

        <template #toolbar>
          <div class="toolbar">
            <template v-if="btnAuthTemplate('分享')">
              <a-button class="share" preIcon="ant-design:share-alt-outlined" @click="visibleModal('share')"> 分享 </a-button>
            </template>
            <template v-if="btnAuthTemplate('批量压缩下载')">
              <a-button class="download" preIcon="ant-design:download-outlined" @click="visibleModal('mulDownload')"> 批量压缩下载 </a-button>
            </template>
            <template v-if="btnAuthTemplate('地图显示')">
              <a-button class="show-in-map" preIcon="ant-design:eye-outlined" @click="handleInMap(1)"> 地图显示 </a-button>
            </template>
            <template v-if="btnAuthTemplate('地图隐藏')">
              <a-button class="show-in-map" type="dashed" preIcon="ant-design:eye-invisible-outlined" @click="handleInMap(0)"> 地图隐藏 </a-button>
            </template>
            <template v-if="btnAuthTemplate('移动文件')">
              <a-button class="addButton" preIcon="ant-design:diff-outlined" @click="visibleModal('moveFile')"> 移动文件 </a-button>
            </template>
            <template v-if="btnAuthTemplate('删除文件')">
              <a-button class="addButton" preIcon="ant-design:delete-outlined" @click="mulDelete"> 删除文件 </a-button>
            </template>

            <template v-if="btnAuthTemplate('上传文件')">
              <a-button class="upload" preIcon="ant-design:cloud-upload-outlined" @click="visibleModal('uploadFile')">
                {{ isModelDir ? '导入模型' : '上传文件' }}
              </a-button>
            </template>

            <template v-if="btnAuthTemplate('新建文件夹')">
              <a-button class="addButton" preIcon="ant-design:plus-outlined" @click="visibleModal('addDir')"> 新建文件夹 </a-button>
            </template>

            <a-select
              style="width: 143px; margin-left: 8px"
              allowClear
              :options="optionsList.isLabel"
              v-model:value="queryParam.isLabel"
              @change="(value) => selectChange('isLabel', value)"
            ></a-select>
            <a-tree-select
              :field-names="{ children: 'children', value: 'id', label: 'name' }"
              v-model:value="queryParam.labelIds"
              style="min-width: 200px; margin-left: 8px"
              :tree-data="optionsList.labelIds"
              tree-checkable
              allow-clear
              :show-checked-strategy="SHOW_CHILD"
              placeholder="请选择标签类型"
              tree-node-filter-prop="name"
              :max-tag-count="3"
              @change="(value) => selectChange('labelIds', value)"
            />
            <a-select
              style="width: 143px; margin-left: 8px"
              v-model:value="queryParam.device"
              allowClear
              :options="optionsList.device"
              @change="(value) => selectChange('device', value)"
            ></a-select>
            <a-select
              style="width: 143px; margin-left: 8px; margin-right: 8px"
              v-model:value="queryParam.fileType"
              allowClear
              :options="optionsList.fileType"
              @change="(value) => selectChange('fileType', value)"
            ></a-select>
            <!-- <a-date-picker
              style="width: 143px"
              valueFormat="YYYY-MM-DD HH:mm:ss"
              format="YYYY-MM-DD HH:mm:ss"
              @change="searchQuery"
              @ok="searchQuery"
              show-time
              v-model:value="queryParam.createTime"
            /> -->

            <a-range-picker v-model:value="dateRange" @change="handleDateRange" />
          </div>
        </template>

        <template #tableTop>
          <div class="folderLevelBox">
            <div class="left">当前位置</div>
            <div class="right">
              <div class="rightBox">
                <div class="box" v-for="(item, index) in dirNavigation" :key="index">
                  <div class="dirIcon"><img :src="jobImgUrl('文件夹-黄', 'svg')" /></div>
                  <div class="text openDir" @click="clickDir(index)">{{ item.name }}</div>
                  <CaretRightOutlined />
                </div>
              </div>
            </div>
            <a-input-search allowClear size="large" class="fileNameInputSearch" v-model:value="queryParam.fileName" placeholder="请输入文件名称" />
          </div>
        </template>

        <!-- <template #action="{ record, column }">

        </template> -->
      </BasicTable>
    </div>

    <ImagePreview :imageList="imgList"></ImagePreview>
    <uploadFileModal :dirList="dirNavigation" @success="success" @register="registerUploadFileModal" :isModelDir="isModelDir"></uploadFileModal>
    <AddDirModal :dirList="dirNavigation" @success="success" @register="registerAddDir"></AddDirModal>
    <OpenDownload @success="success" @register="registerDownload"></OpenDownload>
    <OpenShare @success="success" @register="registerShare"></OpenShare>
    <moveFileModal
      :moveType="moveType"
      :dirList="dirNavigation"
      :fileId="curFileId"
      :fileName="curFileName"
      :dirId="curDirId"
      :fileIdList="fileIdList"
      :oldDirIdList="oldDirIdList"
      @success="success"
      @register="registerMoveFile"
    ></moveFileModal>
    <a-modal title="建模" v-model:visible="isModalVisible" @ok="handleOk" @cancel="handleCancel" :getContainer="() => $refs.modelModal">
      <a-form :model="modelForm" ref="modelFormRef">
        <a-form-item
          label="模型名称"
          name="modelingName"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
          :rules="[{ required: true, message: '请输入模型名称' }]"
        >
          <a-input v-model:value="modelForm.modelingName" />
        </a-form-item>
        <a-form-item
          label="模型类型"
          name="sysFileType"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 20 }"
          :rules="[{ required: true, message: '请选择模型类型' }]"
        >
          <a-select v-model:value="modelForm.sysFileType">
            <a-select-option value="4">正射影像</a-select-option>
            <a-select-option value="5">三维模型</a-select-option>
            <a-select-option value="7">点云</a-select-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" name="dataManage-dataManage" setup>
import uploadFileModal from './components/uploadFileModal.vue';
import moveFileModal from './components/moveFileModal.vue';
import AddDirModal from './components/addDirModal.vue';
import OpenDownload from './components/openDownload.vue';
import OpenShare from './components/openShare.vue';
import { useRoute, useRouter, onBeforeRouteEnter } from 'vue-router';
import { onUnmounted, onMounted, onActivated, reactive, watch, watchEffect, ref, computed, toRaw, nextTick, createVNode } from 'vue';
import { BasicTable, useTable, TableAction, EditRecordRow, BasicColumn, ActionItem } from '/@/components/Table';
import { useMessage } from '/@/hooks/web/useMessage';
import { cloneDeep, template, debounce } from 'lodash-es';
import { useModal } from '/@/components/Modal';
import { getIconUrl } from '/@/utils';
import { render } from '/@/utils/common/renderUtils';
import { createImgPreview, ImagePreview } from '/@/components/Preview/index';
import { ImageProps } from '/@/components/Preview/src/typing';
import { useMultipleTabStore } from '/@/store/modules/multipleTab';
import QRCode from 'qrcode'; // 引入qrcode库
import {
  postList,
  getDirDictionary,
  putDirRename,
  deteleDirDelete,
  delSingleFile,
  putRenameFile,
  buildModel,
  showMapBatch,
  delFileBatch,
  singleDownload,
  mulDownload,
  postDownloadFile,
  deleteShare,
  cancelShare,
} from './data.api';
import {
  CaretRightOutlined,
  FolderOpenFilled,
  SearchOutlined,
  InfoCircleOutlined,
  ExclamationCircleOutlined,
  QrcodeOutlined,
} from '@ant-design/icons-vue';
import { DataDirNavigation, DataTablePagination, ProjectIdCache, DataSearchCondition } from '/@/constants/index';
import { mediaUploadStatus } from '/@/constants/job';
import IMG from '/@/assets/images/Img.jpg';
import { initDictOptions } from '/@/utils/dict/JDictSelectUtil';
import { Progress, Tooltip, TreeSelect, Modal, message, Popover } from 'ant-design-vue';
import { tagManageApi } from '../tagManage/tagManage.api';
import { usePermission } from '/@/hooks/web/usePermission';
import dayjs from 'dayjs';
import { getDepartNameByCode } from '/@/utils/common/compUtils';
const qrCodeUrl = ref(null);
const SHOW_CHILD = TreeSelect.SHOW_CHILD;
const router = useRouter();
const route = useRoute();
const isModalVisible = ref(false);
const modelForm = ref({
  modelingName: '',
  sysFileType: null,
});
const modelFormRef = ref(null);
const modelStatus = ref([]);
const modelDirId = ref('');
const jobImgUrl = (name: string, suffix = 'png') => {
  return getIconUrl(name, 'icons/', suffix);
};
const tabStore = useMultipleTabStore();
let imgList: ImageProps[] = [];
const tableRef = ref();
const dateRange = ref([]);

const selectedKeys = ref<string[]>([]); // 选中状态，类似于 “高亮” 或 “聚焦”，数组的形式是为了配合multiple，可实现多个选中状态
const checkedKeys = ref<string[]>([]); // 勾选状态，类似于 “打勾” 或 “批量选择”
const treeData = ref([]);
const isMyPart = ref(false);
const isInitializing = ref(true); // 标记是否正在初始化
const fileLabelStatusMap = {
  '0': {
    txt: '未标注',
    cls: 'notMark',
  },
  '1': {
    txt: '已标注',
    cls: 'allMark',
  },
  '10': {
    txt: '待确认',
    cls: 'waitMark',
  },
  null: {
    txt: '--',
    cls: '',
  },
  '': {
    txt: '--',
    cls: '',
  },
};
const showMapStatus = {
  0: '否',
  1: '是',
  null: '--',
};
const dirLabelStatusMap = {
  '0': {
    txt: '全部未标注',
    cls: 'notMark',
  },
  '1': {
    txt: '全部已标注',
    cls: 'allMark',
  },
  '2': {
    txt: '部分标注',
    cls: 'partMark',
  },
  '10': {
    txt: '待确认',
    cls: 'waitMark',
  },
  null: {
    txt: '--',
    cls: '',
  },
  '': {
    txt: '--',
    cls: '',
  },
};
const optionsList = ref({
  device: [
    {
      value: '',
      label: '全部设备',
    },
  ],
  isLabel: [
    {
      value: null,
      label: '全部',
    },
    {
      value: '1',
      label: '已标注',
    },
    {
      value: '0',
      label: '未标注',
    },
    {
      value: '10',
      label: '待确认',
    },
  ],
  dirType: [],
  fileType: [
    {
      value: '',
      label: '全部文件类型',
    },
  ],
  labelIds: [],
}); // 字典数据
const columns = [
  {
    title: '名称',
    dataIndex: 'fileName',
    key: 'fileName',
    align: 'left',
    width: 300,
    ellipsis: true,
    sorter: true,
  },
  {
    title: '所属部门',
    dataIndex: 'myDepartName',
    key: 'myDepartName',
    width: 200,
    align: 'center',
  },
  {
    title: '文件类型',
    dataIndex: 'fileType',
    key: 'fileType',
    width: 120,
    align: 'center',
  },
  {
    title: '地图显示',
    dataIndex: 'showMap',
    key: 'showMap',
    width: 100,
    align: 'center',
  },
  {
    title: '拍摄设备',
    dataIndex: 'deviceName',
    key: 'deviceName',
    // width: '150px',
    align: 'center',
    customRender: function ({ text, record }) {
      const { payload } = record as any;
      return `${text || ''} ${payload || ''}`;
    },
  },
  {
    title: '大小',
    dataIndex: 'fileSize',
    key: 'fileSize',
    width: 100,
    align: 'center',
    sorter: true,
    customRender: function ({ record }) {
      const { isDir, dirSize, fileSize } = record as any;
      const size = isDir ? dirSize : fileSize;
      const isG = size >= 1024 * 1024 * 1024;
      const isM = size >= 1024 * 1024;
      const isKB = size >= 1024;
      const text = size
        ? isG
          ? (size / (1024 * 1024 * 1024)).toFixed(2)
          : isM
          ? (size / (1024 * 1024)).toFixed(2)
          : isKB
          ? (size / 1024).toFixed(2)
          : Number(size || 0)
          ? size.toFixed(2)
          : 0
        : 0;
      return `${text}${isG ? 'G' : isM ? 'M' : isKB ? 'KB' : 'B'}`;
    },
  },
  {
    title: '标注状态',
    dataIndex: 'labelStatus',
    key: 'labelStatus',
    width: 120,
    align: 'center',
  },
  {
    title: '上传状态',
    dataIndex: 'modelingStatus',
    key: 'modelingStatus',
    width: 150,
    align: 'center',
  },
  {
    title: '建模进度',
    dataIndex: 'modelingProgress',
    key: 'modelingProgress',
    width: 150,
    align: 'center',
  },
  {
    title: '压缩进度',
    dataIndex: 'shrinkProgress',
    key: 'shrinkProgress',
    width: 150,
    align: 'center',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    width: 150,
    align: 'center',
    sorter: true,
  },
  {
    title: '分享状态',
    dataIndex: 'shareStatus',
    key: 'shareStatus',
    width: 150,
    align: 'center',
    customRender: function ({ record }) {
      const { fileDirectoryShareInfo } = record as any;
      return fileDirectoryShareInfo?.statusCn;
    },
  },
  {
    title: '浏览次数',
    dataIndex: 'viewCounts',
    key: 'viewCounts',
    width: 150,
    align: 'center',
    customRender: function ({ record }) {
      const { fileDirectoryShareInfo } = record as any;
      return fileDirectoryShareInfo?.numViews + '次';
    },
  },
  {
    title: '操作',
    dataIndex: 'action',
    key: 'action',
    width: 300,
    align: 'center',
  },
];

const resultStatus: any[] = [
  {
    value: mediaUploadStatus.Success,
    label: '上传成功',
  },
  {
    value: mediaUploadStatus.PartialSuccess,
    label: '部分完成',
  },
  {
    value: mediaUploadStatus.Progress,
    label: '上传中',
  },
  {
    value: mediaUploadStatus.Failed,
    label: '上传失败',
  },
];
const iconList: any[] = [
  {
    value: mediaUploadStatus.Success,
    icon: 'success',
  },
  {
    value: mediaUploadStatus.PartialSuccess,
    icon: 'success',
  },
  {
    value: mediaUploadStatus.Progress,
    icon: 'processing',
  },
  {
    value: mediaUploadStatus.Failed,
    icon: 'error',
  },
];
const filterColumns = ref([]);
const { createMessage, createConfirm } = useMessage();
const PAGE_SIZE = 10;
let queryParam = reactive({
  fileType: '',
  device: '',
  // createTime: '',
  beginTime: '',
  endTime: '',
  fileName: '',
  parentId: '0',
  pageNo: 1,
  pageSize: PAGE_SIZE,
  isLabel: null,
  labelIds: route.query.labelIds ? JSON.parse(route.query.labelIds) : [],
  column: 'createTime',
  order: 'desc',
});
console.log('labelIds', route.query.labelIds);
//注册table数据
const [registerTable, { getPaginationRef, setPagination, getDataSource, getSelectRows, reload, setProps, setSelectedRowKeys }] = useTable({
  title: '数据管理',
  api: postList,
  afterFetch: (res) => {
    afterFetch(res);
  },
  rowKey: 'id',
  columns: filterColumns.value,
  searchInfo: {
    parentId: queryParam.parentId,
    isLabel: queryParam.isLabel,
    labelIds: queryParam.labelIds,
  },
  beforeFetch: (params) => {
    // 确保在请求数据前selectedKeys已经有值
    if (selectedKeys.value.length > 0) {
      params.sysOrgCode = selectedKeys.value[0];
      params.deptQueryType = 'CURRENT';
    }
    return params;
  },
  clickToRowSelect: false,
  striped: true,
  useSearchForm: false,
  showTableSetting: false,
  bordered: true,
  showIndexColumn: false,
  canResize: false,
  immediate: false, // 禁止自动加载，手动控制加载时机
  // showActionColumn: true,
  // actionColumn: {
  //   width: 100,
  //   title: '操作',
  //   fixed: 'right',
  //   dataIndex: 'action',
  //   slots: { customRender: 'action' },
  // },
});
/**
 * 选择列配置
 */
const rowSelection = ref();

const editableData = ref<any>({}); // 可编辑行数据(修改文件夹名称)
const [registerUploadFileModal, { openModal }] = useModal();
const [registerAddDir, { openModal: addDirModal }] = useModal();
const [registerMoveFile, { openModal: openMoveFile }] = useModal();
const [registerDownload, { openModal: openDownload }] = useModal();
const [registerShare, { openModal: openShare }] = useModal();

// 文件夹导航
const dirNavigation = ref<any[]>([
  {
    name: '根目录',
    id: '0',
    dirType: '', //文件夹类型
  },
]);
console.log('dirNavigation', dirNavigation.value);
const moveType = ref<string>('');
// 移动文件/文件夹
let curFileId = ref<string | number>('');
let curFileName = ref<string>('');
let curDirId = ref<string>('');
const fileIdList = ref([]);
const oldDirIdList = ref([]);
const input_focus = ref(null);

/**
 * 根据记录中的建模状态生成进度文本
 *
 * @param record 记录对象
 * @returns 进度文本
 */
const progressText = (record) => {
  const { modelingStatus } = record as any;
  return modelStatus.value?.find((item) => item.value == modelingStatus)?.label || '';
};
const toQrcode = async (shareUrl) => {
  const qrCodeDataUrl = await QRCode.toDataURL(shareUrl);
  return qrCodeDataUrl;
};

const afterFetch = async (res) => {
  for (const item of res) {
    if (item.fileDirectoryShareInfo?.shareUrl) {
      // 获取二维码的 DataURL
      item.fileDirectoryShareInfo = {
        ...item.fileDirectoryShareInfo,
        toQrcode: await toQrcode(item.fileDirectoryShareInfo.shareUrl), // 等待二维码生成完毕
      };

      console.log('afterFetch', res);
    }
  }
};
const handleDateRange = (val) => {
  console.log('val', val);
  queryParam.beginTime = val ? dayjs(val[0].$d).format('YYYY-MM-DD 00:00:00') : '';
  queryParam.endTime = val ? dayjs(val[1].$d).format('YYYY-MM-DD 23:59:59') : '';
  console.log('queryParam==>>', queryParam);
};
const progressStatus = (record) => {
  const { modelingStatus, modelingProgress } = record as any;
  if (modelingStatus === 40 || modelingStatus === 50) {
    // 任务失败或者任务取消，显示失败
    return 'exception';
  } else if (modelingStatus === 60 && modelingProgress === 100) {
    // 任务完成，并且进度条100%
    return 'success';
  } else {
    return 'active';
  }
};
const shrinkProgressStatus = (record) => {
  const { status, progress } = record.exportTaskInfo;
  if (status === '40') {
    // 任务失败，显示失败原因
    return 'exception';
  } else if (status === '30' && progress === 100) {
    // 任务完成，并且进度条100%
    return 'success';
  } else {
    return 'active';
  }
};

const handleInMap = (val) => {
  Modal.confirm({
    title: val ? '确认所选内容执行批量地图显示操作?' : '确认所选内容批量关闭地图显示操作?',
    icon: createVNode(ExclamationCircleOutlined),
    content: val ? '执行批量地图显示后,数据可在地图相关页面中显示数据信息' : '批量关闭地图显示后,数据在地图相关页面中不显示',
    okType: val ? 'primary' : 'danger',
    onOk: async () => {
      const selectRows: any[] = getSelectRows();
      let dirIdList = [];
      let fileIdList = [];
      selectRows?.forEach((item) => {
        if (item.isDir) {
          dirIdList.push(item.id);
        } else {
          fileIdList.push(item.id);
        }
      });
      console.log('dirIdList==>>', dirIdList);
      console.log('fileIdList==>>', fileIdList);

      await showMapBatch({ dirIdList, fileIdList, showMap: val });
      searchQuery();
    },
    onCancel() {},
  });
  console.log('inMap', val);
};

const mulDelete = () => {
  Modal.confirm({
    title: '确认批量删除所选文件?',
    icon: createVNode(ExclamationCircleOutlined),
    okType: 'danger',
    onOk: async () => {
      const selectRows: any[] = getSelectRows();
      let dirIdList = [];
      let fileIdList = [];
      selectRows?.forEach((item) => {
        if (item.isDir) {
          dirIdList.push(item.id);
        } else {
          fileIdList.push(item.id);
        }
      });
      const res = await delFileBatch({ dirIdList, fileIdList });
      const { data } = res;
      if (data.code === 200) {
        createMessage.success(data.message);
        searchQuery();
      } else {
        createMessage.error(data.message);
      }
    },
    onCancel() {},
  });
};
const copyLink = (record) => {
  const { fileDirectoryShareInfo } = record;
  const link = fileDirectoryShareInfo.shareUrl;
  const password = fileDirectoryShareInfo.passWord;
  const textToCopy = password ? `链接：${link} 密码：${password}` : `链接：${link}`;
  if (navigator.clipboard && navigator.clipboard.writeText) {
    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        createMessage.success('复制成功！');
      })
      .catch(() => {
        fallbackCopyTextToClipboard(textToCopy);
        console.log('使用了备用方案复制');
      });
  } else {
    fallbackCopyTextToClipboard(textToCopy);
    console.log('使用了备用方案复制2');
  }
};

// 备选方案：使用 document.execCommand('copy')
const fallbackCopyTextToClipboard = (text) => {
  const textArea = document.createElement('textarea');
  textArea.value = text;
  document.body.appendChild(textArea);
  textArea.select();
  try {
    const successful = document.execCommand('copy');
    if (successful) {
      createMessage.success('复制成功！');
    } else {
      createMessage.error('复制失败，请手动复制。');
    }
  } catch (err) {
    console.error('备选方案复制失败：', err);
    createMessage.error('复制失败，请手动复制。');
  }
  document.body.removeChild(textArea);
};

const showModelModal = (record) => {
  isModalVisible.value = true;
  modelForm.value.modelingName = record.directoryName;
  modelDirId.value = record.dirId;
};
const handleOk = () => {
  modelFormRef.value
    .validate()
    .then(async () => {
      const res = await buildModel({ ...modelForm.value, dirId: modelDirId.value });
      // 校验通过，执行后续操作
      isModalVisible.value = false;
      modelFormRef.value.resetFields();
    })
    .catch((errorInfo) => {
      // 校验未通过
      console.error('校验未通过:', errorInfo);
    });
};
const handleCancel = () => {
  isModalVisible.value = false;
  modelFormRef.value.resetFields();
};
const winOpen = () => {
  // window.open('http://110.41.164.180:18080/skyline-report.html')
  window.open('/skyline-report.pdf');
};
watch(selectedKeys, () => {
  localStorage.setItem('selectedDepart', JSON.stringify(selectedKeys.value));
  const myDepartList = localStorage.getItem('myDepartList');
  const parsedDepartList = myDepartList ? JSON.parse(myDepartList) : [];
  isMyPart.value = parsedDepartList.some((item) => item.orgCode === selectedKeys.value[0]);
  queryParam.parentId = '0';
  dirNavigation.value = [
    {
      name: '根目录',
      id: '0',
      dirType: '', //文件夹类型
    },
  ];
  // 只有在非初始化状态时才调用reload，避免重复请求
  if (!isInitializing.value) {
    reload();
  }
});
watch(checkedKeys, () => {
  console.log('checkedKeys', checkedKeys.value);
});

// 监听路由参数变化，更新树选中项
watch(
  () => route.query.sysOrgCode,
  (newSysOrgCode, oldSysOrgCode) => {
    // 只有在初始化完成后且 sysOrgCode 确实发生变化时才处理
    if (!isInitializing.value && newSysOrgCode && typeof newSysOrgCode === 'string' && newSysOrgCode !== oldSysOrgCode) {
      // 获取树数据
      const treeObj = localStorage.getItem('myDepartAndChildrenTree');
      const rawTreeData = treeObj ? JSON.parse(treeObj) : [];

      // 验证新的 sysOrgCode 是否有效
      const isValidOrgCode = validateOrgCodeInTree(rawTreeData, newSysOrgCode);
      if (isValidOrgCode) {
        selectedKeys.value = [newSysOrgCode];
        console.log('路由参数变化，更新选中项:', newSysOrgCode);
      } else {
        console.warn('路由参数中的 sysOrgCode 无效:', newSysOrgCode);
      }
    }
  },
  { immediate: false } // 不立即执行，避免与初始化冲突
);

// 监听路由完整变化，检测缓存页面的重新进入
watch(
  () => route.fullPath,
  async (newPath, oldPath) => {
    console.log('🔍 路由完整路径变化:', { newPath, oldPath });

    // 只有在非初始化状态且路径包含数据管理页面时才处理
    if (!isInitializing.value && newPath.includes('/dataManage/dataManage')) {
      console.log('🔍 检测到重新进入数据管理页面，检查缓存更新');

      // 重新读取缓存，因为其他页面可能已经更新了缓存
      const needLoad = await getDirCache();
      if (needLoad) {
        console.log('🔍 缓存已更新，重新加载数据');
        searchQuery();
      }
    }
  },
  { immediate: false }
);

//选中状态不可取消，保持单选状态
const onSelect = (keys, { node }) => {
  if (keys.length === 0) {
    // 不允许取消，保持原来的选中
    selectedKeys.value = [node.key];
  } else {
    // 单选，只保留最后一个
    selectedKeys.value = [keys[keys.length - 1]];
  }
  console.log('选中onSelect', selectedKeys.value);
};

watch(
  () => router.options.history.state,
  async (newValue, oldValue) => {
    // 如果正在初始化，跳过路由监听器的处理，避免干扰初始化流程
    if (isInitializing.value) {
      console.log('🔍 路由状态变化，但正在初始化，跳过处理');
      return;
    }

    //const { back, replaced } = newValue
    const curProjectId = localStorage.getItem('Project_Id');
    const projectIdCache = sessionStorage.getItem(ProjectIdCache);
    console.log('🔍 路由状态变化，curProjectId:', curProjectId, 'projectIdCache:', projectIdCache);
    if (JSON.stringify(curProjectId) === projectIdCache) {
      // 缓存中获取数据的场景： 项目id不变更
      console.log('🔍 项目ID未变更，从缓存加载数据');
      const needLoad = await getDirCache();
      if (needLoad) {
        searchQuery();
      }
    } else {
      console.log('🔍 项目ID变更，重置到根目录');
      queryParam.parentId = '0';
      queryParam.pageNo = 1;
      queryParam.pageSize = PAGE_SIZE;
      sessionStorage.setItem(ProjectIdCache, JSON.stringify(curProjectId)); // 更新页面中缓存的项目id
      searchQuery();
    }
  },
  {}
);
// 监听目录变化
watch(
  () => dirNavigation.value,
  async (newValue, oldValue) => {
    const currentNav = newValue[newValue?.length - 1];
    const preNav = oldValue?.[oldValue?.length - 1];
    console.log('currentNav', currentNav);
    console.log('preNav', preNav);
    console.log('newValue', newValue);
    console.log('oldValue', oldValue);
    //根目录、共享文件夹时，不展示第一列多选框
    if (currentNav.id === '0' || currentNav.dirType == '21') {
      rowSelection.value = null;
    } else {
      rowSelection.value = {
        type: 'checkbox',
        columnWidth: 50,
        onChange: (selectedRowKeys, selectedRows) => {
          console.log('Selected Row Keys: ', selectedRowKeys);
          console.log('Selected Rows: ', selectedRows);
        },
      };
    }
    //跨目录时，清空选中行信息（防止用户在多选不同目录下，误删误操作数据）
    if (currentNav?.id !== preNav?.id) {
      // await nextTick();
      if (tableRef.value) {
        setSelectedRowKeys([]);
      }
    }

    // 不同目录下，table列表展示不同的信息
    if (currentNav.dirType === '') {
      // 根目录
      filterColumns.value = columns.filter((item) => ['fileName', 'fileSize', 'createTime', 'myDepartName'].includes(item.dataIndex));
    } else if (currentNav.dirType === '1' && newValue?.length === 2) {
      // 根目录/模型库
      filterColumns.value = columns.filter((item) =>
        ['fileName', 'fileSize', 'createTime', 'fileType', 'modelingProgress', 'showMap', 'myDepartName'].includes(item.dataIndex)
      );
    } else if (currentNav.dirType === '1' && newValue?.length >= 2) {
      // 根目录/模型库/模型文件夹
      filterColumns.value = columns.filter((item) =>
        ['fileName', 'fileSize', 'createTime', 'fileType', 'showMap', 'action', 'myDepartName'].includes(item.dataIndex)
      );
    } else if (currentNav.dirType === '2' && newValue?.length === 2) {
      // 根目录/设备文件夹
      filterColumns.value = columns.filter((item) =>
        ['fileName', 'fileSize', 'createTime', 'fileType', 'modelingStatus', 'showMap', 'labelStatus', 'action', 'myDepartName'].includes(
          item.dataIndex
        )
      );
    } else if (currentNav.dirType === '2' && newValue?.length >= 2) {
      // 根目录/设备文件夹/任务文件夹
      filterColumns.value = columns.filter((item) =>
        ['fileName', 'fileSize', 'createTime', 'fileType', 'deviceName', 'showMap', 'labelStatus', 'action', 'myDepartName'].includes(item.dataIndex)
      );
    } else if (currentNav.dirType === '3') {
      // 用户文件夹
      filterColumns.value = columns.filter((item) =>
        ['fileName', 'fileSize', 'createTime', 'fileType', 'deviceName', 'showMap', 'action', 'myDepartName'].includes(item.dataIndex)
      );
    } else if (currentNav.dirType === '20') {
      // 批量下载文件夹
      filterColumns.value = columns.filter((item) =>
        ['fileName', 'fileSize', 'createTime', 'shrinkProgress', 'action', 'myDepartName'].includes(item.dataIndex)
      );
    } else if (currentNav.dirType === '21') {
      // 共享文件夹
      filterColumns.value = columns.filter((item) =>
        ['fileName', 'createTime', 'shareStatus', 'viewCounts', 'action', 'myDepartName'].includes(item.dataIndex)
      );
    } else {
    }
    await nextTick();
    setProps({ columns: filterColumns.value });
  },
  { deep: true, immediate: true }
);
watch(
  [
    () => queryParam.isLabel,
    () => queryParam.labelIds,
    () => queryParam.device,
    () => queryParam.fileType,
    () => queryParam.beginTime,
    () => queryParam.endTime,
  ],
  ([isLabel, labelIds, device, fileType, beginTime, endTime], oldValues) => {
    if (JSON.stringify([isLabel, labelIds, device, fileType, beginTime, endTime]) === JSON.stringify(oldValues)) {
      // 参数没有变化，不执行任何操作
      return;
    }

    // 如果正在初始化，不触发搜索，避免重复调用
    if (isInitializing.value) {
      console.log('搜索条件变化，但正在初始化，跳过搜索');
      return;
    }

    // // 只要搜索条件变化，就只在根目录下执行。
    // dirNavigation.value = [
    //   {
    //     name: '根目录',
    //     id: '0',
    //     dirType: '', //文件夹类型
    //   },
    // ];
    // sessionStorage.setItem(DataDirNavigation, JSON.stringify(dirNavigation.value));

    // 所有的搜索条件为初始状态
    // const allInitial =
    //   isLabel === null && labelIds.length === 0 && device === '' && fileType === '' && fileName === '' && beginTime === '' && endTime === '';

    const dirCache = JSON.parse(sessionStorage.getItem(DataDirNavigation) || '[]');

    const newParentId = dirCache?.[dirCache?.length - 1]?.id;

    queryParam.parentId = newParentId;

    // if (!allInitial) {
    //   // 存在搜索条件时，列表需要显示的列字段
    //   filterColumns.value = columns.filter((item) => ['fileName', 'fileType', 'fileSize', 'createTime', 'action'].includes(item.dataIndex));
    //   //存在搜索条件时，展示第一列多选框
    //   // rowSelection.value = {
    //   //   type: 'checkbox',
    //   //   columnWidth: 50,
    //   //   onChange: (selectedRowKeys, selectedRows) => {
    //   //     console.log('Selected Row Keys: ', selectedRowKeys);
    //   //     console.log('Selected Rows: ', selectedRows);
    //   //   },
    //   // };
    //   // rowSelection.value = null;
    // } else {
    //   filterColumns.value = columns.filter((item) => ['fileName', 'fileSize', 'createTime'].includes(item.dataIndex));

    //   //在根目录时，不展示展示第一列多选框
    //   if (newParentId === '0') {
    //     rowSelection.value = null;
    //   }
    // }
    console.log('搜索条件watch', filterColumns.value);
    setProps({ columns: filterColumns.value });
    searchQuery();
  }
);
watch(
  // 单独监听fileName,加上防抖
  () => queryParam.fileName,
  (newVal, oldVal) => {
    if (newVal === oldVal) return;
    debouncedSearch();
  }
);
// 从缓存中获取当前目录的路径
const getDirCache = async () => {
  const dirCache = sessionStorage.getItem(DataDirNavigation);
  console.log('🔍 getDirCache 检查缓存:', dirCache);

  // 页签切换已经设置缓存，无需再缓存
  // let paginationCache: any = sessionStorage.getItem(DataTablePagination);
  // console.log('555paginationCache', paginationCache);
  // paginationCache = paginationCache && JSON.parse(paginationCache);
  if (dirCache && dirCache !== '') {
    const dirNavigationCache = JSON.parse(dirCache);
    const newParentId = dirNavigationCache[dirNavigationCache?.length - 1]?.id;
    console.log('🔍 从缓存获取目录路径，dirNavigationCache:', dirNavigationCache);
    console.log('🔍 从缓存获取目录路径，newParentId:', newParentId);
    console.log('🔍 当前 queryParam.parentId:', queryParam.parentId);
    console.log('🔍 当前 dirNavigation:', JSON.stringify(dirNavigation.value));

    // 检查缓存是否真的发生了变化
    const currentDirNavigationStr = JSON.stringify(dirNavigation.value);
    const cacheDirNavigationStr = JSON.stringify(dirNavigationCache);
    const hasNavigationChanged = currentDirNavigationStr !== cacheDirNavigationStr;
    const hasParentIdChanged = newParentId != queryParam.parentId;

    console.log('🔍 导航路径是否变化:', hasNavigationChanged);
    console.log('🔍 parentId是否变化:', hasParentIdChanged);

    if (hasNavigationChanged || hasParentIdChanged) {
      dirNavigation.value = dirNavigationCache;
      queryParam.parentId = newParentId;
      console.log('🔍 更新目录路径，新的 parentId:', newParentId);

      // 标签查询时，只有根目录
      if (route.query.labelIds) {
        queryParam.parentId = '0';
        console.log(456456);
        dirNavigation.value = [
          {
            name: '根目录',
            id: '0',
            dirType: '', //文件夹类型
          },
        ];
      }

      // 重置分页
      queryParam.pageNo = 1;
      queryParam.pageSize = PAGE_SIZE;
      setPagination({
        current: 1,
        pageSize: PAGE_SIZE,
      });

      // 返回 true 表示需要重新加载数据
      return true;
    } else {
      console.log('🔍 缓存未变化，无需重新加载');
      return false;
    }
  } else {
    // 没有缓存时，使用根目录作为默认值
    console.log('🔍 没有目录缓存，使用根目录');
    queryParam.parentId = '0';
    dirNavigation.value = [
      {
        name: '根目录',
        id: '0',
        dirType: '', //文件夹类型
      },
    ];
    queryParam.pageNo = 1;
    queryParam.pageSize = PAGE_SIZE;
    setPagination({
      current: 1,
      pageSize: PAGE_SIZE,
    });
    return true;
  }
};
const getDictOptions = () => {
  initDictOptions('model_status').then((res) => {
    modelStatus.value = res;
    console.log('modelStatus', modelStatus.value);
  });
};

const getLabelList = () => {
  tagManageApi.getList().then((res) => {
    console.log('getLabelList=>>', res);
    optionsList.value.labelIds = res;
  });
};

const labelStatusObj = (record) => {
  return record.isDir ? dirLabelStatusMap[record.dirLabelStatus] : fileLabelStatusMap[record.fileLabelStatus];
};
// 单选时转换字段以便使用tree组件的disabled字段
const transformTreeData = (data) => {
  return data.map((node) => {
    const newNode = {
      ...node,
      disabled: node.disableCheckbox,
    };
    delete newNode.disableCheckbox;

    if (node.children) {
      newNode.children = transformTreeData(node.children);
    }

    return newNode;
  });
};

// 验证 orgCode 是否在树数据中存在
const validateOrgCodeInTree = (treeData, orgCode) => {
  const findInTree = (nodes, targetCode) => {
    for (const node of nodes) {
      if (node.orgCode === targetCode) {
        return true;
      }
      if (node.children && findInTree(node.children, targetCode)) {
        return true;
      }
    }
    return false;
  };
  return findInTree(treeData, orgCode);
};

// 设置默认选中项
const setDefaultSelectedKey = (myDepartList) => {
  if (myDepartList) {
    const parsedDepartList = JSON.parse(myDepartList);
    if (parsedDepartList.length > 0) {
      selectedKeys.value = [parsedDepartList[0].orgCode];
      console.log('使用默认部门设置选中项:', parsedDepartList[0].orgCode);
    }
  }
};

const setDepartmentTree = async () => {
  // 获取部门树
  const treeObj = localStorage.getItem('myDepartAndChildrenTree');
  // 获取所属部门
  const myDepartList = localStorage.getItem('myDepartList');

  const rawTreeData = treeObj ? JSON.parse(treeObj) : [];

  treeData.value = transformTreeData(rawTreeData);
  console.log('myDepartList', myDepartList);

  // 优先使用路由参数中的 sysOrgCode
  const routeSysOrgCode = route.query.sysOrgCode as string;

  if (routeSysOrgCode) {
    // 验证路由参数中的 sysOrgCode 是否在树数据中存在
    const isValidOrgCode = validateOrgCodeInTree(rawTreeData, routeSysOrgCode);
    if (isValidOrgCode) {
      selectedKeys.value = [routeSysOrgCode];
      console.log('使用路由参数设置选中项:', routeSysOrgCode);
    } else {
      console.warn('路由参数中的 sysOrgCode 在树数据中不存在:', routeSysOrgCode);
      // 如果路由参数无效，使用默认逻辑
      setDefaultSelectedKey(myDepartList);
    }
  } else {
    // 没有路由参数时使用默认逻辑
    setDefaultSelectedKey(myDepartList);
  }

  console.log('treeData', treeData.value);
};

// 监听页面可见性变化，检测选项卡切换时的缓存更新
const handleVisibilityChange = async () => {
  if (!document.hidden && !isInitializing.value) {
    console.log('🔍 页面变为可见，检查缓存是否更新');
    const needLoad = await getDirCache();
    if (needLoad) {
      console.log('🔍 检测到缓存更新，重新加载数据');
      searchQuery();
    }
  }
};

// 缓存组件激活时检查缓存更新
const handleActivated = async () => {
  if (!isInitializing.value) {
    console.log('🔍 缓存组件被激活，检查缓存是否更新');
    const needLoad = await getDirCache();
    if (needLoad) {
      console.log('🔍 检测到缓存更新，重新加载数据');
      searchQuery();
    }
  }
};

onMounted(async () => {
  console.log('🔍 onMounted 开始初始化');
  console.log('🔍 路由参数:', route.query);
  console.log('🔍 当前缓存状态:', sessionStorage.getItem(DataDirNavigation));

  // 先设置部门树，但不触发数据加载
  await setDepartmentTree();

  getLabelList();

  const ProjectId = localStorage.getItem('Project_Id');
  sessionStorage.setItem(ProjectIdCache, JSON.stringify(ProjectId));
  // 初始化字典
  const res = await getDirDictionary({});
  console.log('result', res);
  if (Object.keys(res)?.length) {
    Object.keys(res).forEach((key) => {
      if (['device', 'dirType', 'fileType'].includes(key)) {
        if (['device', 'fileType'].includes(key)) {
          optionsList.value[key] = optionsList.value[key].concat(res[key]);
        } else {
          optionsList.value[key] = res[key];
        }
      }
    });
  }
  // 获取建模状态字典
  getDictOptions();

  // 检查是否需要从缓存加载数据
  const needLoadFromCache = await getDirCache();
  console.log('🔍 needLoadFromCache:', needLoadFromCache);
  console.log('🔍 最终 queryParam:', queryParam);

  // 初始化完成，允许后续的selectedKeys变化触发reload
  isInitializing.value = false;

  // 统一在最后加载数据，避免重复调用
  if (needLoadFromCache) {
    console.log('🔍 调用 searchQuery');
    searchQuery();
  } else {
    // 如果没有缓存，直接加载根目录数据
    console.log('🔍 调用 reload');
    reload();
  }

  // 添加页面可见性监听器
  document.addEventListener('visibilitychange', handleVisibilityChange);
});

// 缓存组件激活时的生命周期钩子
onActivated(handleActivated);

onUnmounted(() => {
  const index = getTabsState.value?.length ? getTabsState.value.findIndex((item) => item.path === '/dataManage/dataManage') : null;
  //console.log('销毁缓存的项目id', index, getTabsState.value)
  if (index === -1 || index === null) {
    // sessionStorage.setItem(DataTablePagination, '');
    sessionStorage.setItem(DataDirNavigation, ''); // 切换目录时清空数据管理的目录路径缓存
    sessionStorage.setItem(ProjectIdCache, ''); //tabs中没有当前页签时就销毁缓存的项目id
    sessionStorage.setItem(DataSearchCondition, '');
  }

  // 清理页面可见性监听器
  document.removeEventListener('visibilitychange', handleVisibilityChange);
});

const resultIcon = computed(() => (type: string, record): any => {
  switch (type) {
    case 'resultStatus': {
      return resultStatus.find((item) => item.value === record.mediaUploadStatus)?.label || '-';
    }
    case 'icon': {
      return iconList.find((item) => item.value === record.mediaUploadStatus)?.icon;
    }
  }
});
// 当前在模型库文件夹中
const isModelDir = computed(() => {
  const curDirListTemp = dirNavigation.value;
  return curDirListTemp?.length > 1 && curDirListTemp[curDirListTemp?.length - 1]?.dirType === '1';
});

const getTabsState = computed(() => {
  return tabStore.getTabList.filter((item) => !item.meta?.hideTab);
});

const getFileNameLabel = computed(() => (record) => {
  const label = optionsList.value.fileType.find((item) => item.value && item.value === record.fileType)?.label || '';
  return label ? render.renderTip(label, 12) : '-';
});

const getCurDirId = computed(() => {
  const curDirListTemp = dirNavigation.value;
  return curDirListTemp[curDirListTemp?.length - 1]?.id;
});

function searchQuery() {
  setProps({ searchInfo: toRaw(queryParam) });
  reload();
}

function success() {
  setSelectedRowKeys([]);
  searchQuery();
}

const tableChange = (pagination, filters, sorter) => {
  const { current, pageSize } = pagination;
  console.log('555tableChange--', pagination);
  console.log('queryParam--', queryParam);
  queryParam.pageNo = current;
  queryParam.pageSize = pageSize;
  console.log('参数pagination', pagination);
  console.log('参数sorter', sorter);
  // 转换order值
  const order = sorter.order === 'descend' ? 'desc' : sorter.order === 'ascend' ? 'asc' : queryParam.order;
  queryParam.column = sorter.field ? sorter.field : queryParam.column;
  queryParam.order = order;
};

function visibleModal(type: string, data?: any) {
  const rootDirType = dirNavigation.value[dirNavigation.value.length - 1].dirType;

  switch (type) {
    case 'uploadFile':
      openModal(true);
      break;
    case 'addDir': // 仅仅
      addDirModal(true);
      break;
    case 'share':
      const selectRows: any[] = getSelectRows();
      console.log('selectRows', selectRows);
      openShare(true, selectRows);

      break;
    case 'mulDownload':
      {
        const selectRows: any[] = getSelectRows();
        console.log('selectRows', selectRows);
        if (!selectRows?.length) {
          createMessage.warning('请勾选一个文件');
          return;
        }
        if (isModelDir.value) {
          createMessage.error('模型库文件不能批量下载！');
          setSelectedRowKeys([]);
          return;
        }

        openDownload(true, selectRows);
      }
      break;
    case 'moveFile':
      {
        const selectRows = getSelectRows();
        //console.log("selectRows", selectRows)
        if (!selectRows?.length) {
          createMessage.warning('请勾选一个文件');
          return;
        }
        console.log('isModelDir', isModelDir);
        if (isModelDir.value) {
          createMessage.error('模型库的文件不能移动！');
          setSelectedRowKeys([]);
          return;
        }
        fileIdList.value = selectRows?.filter((item) => item.isDir === false).map((item) => item.fileId);
        oldDirIdList.value = selectRows?.filter((item) => item.isDir === true).map((item) => item.dirId);
        moveType.value = '';
        openMoveFile(true);
      }
      break;
    case 'openFile': // 打开文件详情
      {
        const { fileId, fileType, url, sysOrgCode } = data;

        // 批量下载文件夹、共享文件夹不能打开文件详情

        if (fileType === '10' || fileType === '11') return;
        // 文件类型直接打开下载
        if (fileType === '0' || fileType === '6') {
          window.open(url);
        } else {
          const pagination = getPaginationRef();
          const { current, pageSize } = pagination;
          // const { fileType, labelIds, column, order } = queryParam;
          console.log('55pagination', pagination);
          // sessionStorage.setItem(DataTablePagination, JSON.stringify(pagination));

          const obj = {
            fileId,
            dirId: getCurDirId.value,
            dirPage: current,
            dirPageSize: pageSize,
            isMyPart: isMyPart.value,
            sysOrgCode,
            ...queryParam,
          };

          console.log('77777obj', obj);
          router.push({
            path: '/dataManage/dataManage/detail',
            query: obj,
          });
        }
      }
      break;
  }
}

// 修改文件/文件夹名称(回车)
const handleEnter = async (e) => {
  //console.log("回车--e", e.target.value)
  const editData = editableData.value;
  const { isDir, dirId, fileId, directoryName, directoryNameCache, fileNameNoSuffix, fileName } = editData;

  if (isDir) {
    if (directoryName) {
      await putDirRename({ id: dirId, newName: directoryName, oldName: directoryNameCache });
      editableData.value = {};
      searchQuery();
    } else {
      editableData.value = {};
    }
  } else {
    if (fileNameNoSuffix) {
      await putRenameFile({
        id: fileId,
        newName: `${fileNameNoSuffix}.${fileName.substring(fileName.lastIndexOf('.') + 1)}`,
        oldName: fileName,
      });
      editableData.value = {};
      searchQuery();
    } else {
      editableData.value = {};
    }
  }
};

const handleAction = async (type: string, record: EditRecordRow) => {
  //console.log("功能操作--record", record)
  const { isDir, dirId, fileName, fileId, directoryName, url, id, fileDirectoryShareInfo } = record;
  const rootDirType = dirNavigation.value[dirNavigation.value.length - 1].dirType;

  switch (type) {
    case '下载':
      {
        const res = await postDownloadFile({ id });
        const { data = {} } = res;
        console.log('data==>>', data, res);
        if (data.code !== 200) {
          message.error(data.message);
        } else {
          window.open(data?.result + '?response-content-type=application/octet-stream');
        }

        // 下述方式不再区分，列表里不区分文件类型，点击下载按钮统一为上述下载方式。
        // if (isDownloadDir.value) {
        //   // 在批量下载文件夹下用的是url直接下载
        //   url && window.open(url + '?response-content-type=application/octet-stream');
        // } else {
        //   // 在其他文件夹下用的是流文件下载
        //   singleDownload({ id }).then((res) => {
        //     console.log('res==>>', res);
        //     const blob = new Blob([res.data], { type: res.headers['content-type'] });

        //     const fileName = res.headers['content-disposition'].split('filename=')[1].replace(/"/g, '');
        //     // 生成 URL
        //     const url = window.URL.createObjectURL(blob);

        //     // 创建 <a> 标签并触发点击下载
        //     const link = document.createElement('a');
        //     link.href = url;
        //     link.setAttribute('download', fileName); // 指定下载后的文件名
        //     document.body.appendChild(link);
        //     link.click();

        //     // 清除 URL 对象
        //     window.URL.revokeObjectURL(url);

        //     // 清理 DOM 中的 <a> 标签
        //     document.body.removeChild(link);
        //   });
        // }
      }
      break;
    case '重命名':
      {
        const editDataTemp = { ...record };
        if (isDir) {
          editDataTemp.directoryNameCache = directoryName;
        } else {
          editDataTemp.fileNameNoSuffix = fileName.substring(0, fileName.lastIndexOf('.'));
        }
        editableData.value = cloneDeep(editDataTemp);
        setTimeout(() => {
          input_focus.value && input_focus.value.focus();
        }, 10);
      }
      break;
    case '移动':
      {
        if (isModelDir.value) {
          createMessage.error('模型库的文件不能移动！');
          return;
        }

        if (rootDirType === '20') {
          createMessage.error('批量下载文件夹的文件不能移动！');
          return;
        }
        if (isDir) {
          curDirId.value = dirId;
          moveType.value = 'dir';
        } else {
          curFileId.value = fileId;
          curFileName.value = fileName;
          moveType.value = 'file';
        }
        openMoveFile(true);
      }
      break;
    case '删除':
      {
        if (isDir) {
          createConfirm({
            title: '删除文件夹',
            content: '确定要删除该文件夹吗？',
            iconType: 'warning',
            onOk: async () => {
              const res = await deteleDirDelete({ id: dirId });
              const { data } = res;
              if (data.code === 200) {
                createMessage.success(data.message);
                searchQuery();
              } else {
                createMessage.error(data.message);
              }
            },
            onCancel: () => {},
          });
        } else {
          createConfirm({
            title: '删除文件',
            content: '确定要删除该文件吗？',
            iconType: 'warning',
            onOk: async () => {
              const res = await delSingleFile({ id: fileId });
              const { data = {} } = res;
              if (data.code === 200) {
                createMessage.success(data.message);
                searchQuery();
              } else {
                createMessage.error(data.message);
              }
            },
            onCancel: () => {},
          });
        }
      }
      break;
    case '建模':
      {
        showModelModal(record);
      }
      break;
    case '复制链接':
      {
        copyLink(record);
      }
      break;
    case '取消分享':
      {
        createConfirm({
          title: '取消分享',
          content: '确定要取消该分享吗？',
          iconType: 'warning',
          onOk: async () => {
            await cancelShare({ fsrId: fileDirectoryShareInfo?.fsrId });
            searchQuery();
          },
          onCancel: () => {},
        });
      }
      break;
    case '删除分享':
      {
        createConfirm({
          title: '删除分享',
          content: '确定要删除该分享记录吗？',
          iconType: 'warning',
          onOk: async () => {
            await deleteShare({ fsrId: fileDirectoryShareInfo?.fsrId });
            searchQuery();
          },
          onCancel: () => {},
        });
      }
      break;
  }
};

//table表格顶部的操作权限控制  true: 有权限  false: 无权限 (用在template上)
const btnAuthTemplate = computed(() => (type: string): boolean => {
  const selectRows: any[] = getSelectRows();
  const curDirListTemp = dirNavigation.value;
  const rootDirType = curDirListTemp[curDirListTemp.length - 1].dirType;
  // console.log('999selectRows', selectRows);
  // console.log('999curDirListTemp', curDirListTemp);
  // console.log('999rootDirType', rootDirType);
  if (!isMyPart.value) {
    return false;
  } else {
    //共享文件夹、根目录没有复选框
    const btnAccess = {
      分享: selectRows?.length && (rootDirType === '1' || rootDirType === '2'), //模型库或者设备文件夹
      批量压缩下载: selectRows?.length && rootDirType === '2', // 只有设备文件夹
      地图显示: selectRows?.length && (rootDirType === '1' || rootDirType === '2'), //模型库或者设备文件夹
      地图隐藏: selectRows?.length && (rootDirType === '1' || rootDirType === '2'), //模型库或者设备文件夹
      删除文件: selectRows?.length, // 多选都有
      上传文件: rootDirType === '1' || rootDirType === '3', // 模型库或者用户文件夹
      新建文件夹: rootDirType === '3', // 用户文件夹
      移动文件: selectRows?.length && rootDirType !== '20', // 批量不能移动
    };
    return btnAccess[type];
  }
});
const { hasPermission } = usePermission();
//每条数据的按钮操作权限控制  true: 有权限  false: 无权限
const btnAuth = (type: string, record?: any): boolean => {
  // const curDirListTemp = dirNavigation.value;
  const { isDir, directoryType, fileType, fileDirectoryShareInfo } = record;
  // console.log('dirType999', dirNavigation.value);
  const rootDirType = dirNavigation.value[dirNavigation.value.length - 1].dirType;
  // directoryType目录类型, 0：根目录，1:模型库, 2：设备文件夹， 3：用户文件夹，10：载荷数据根目录，11: 任务数据目录-载荷数据任务目录，20：批量下载文件夹，21：共享文件夹
  // fileType文件类型, 0：其它，1:普通照片,2:视频,3:全景图,4:正射影像,5:三维图像,6：航线文件,7：点云，10：批量下载，11：数据分享，20：AI截图

  const btnAccess = {
    // 应当根据当前文件的文件类型和目录类型显示按钮。
    下载: ['1', '2', '3', '4', '5', '6', '7', '10', '20'].includes(fileType) || directoryType === '3',
    重命名: ['1', '2', '3', '4', '5', '6', '7', '10', '20'].includes(fileType) || directoryType === '3',
    移动: ['1', '2', '3', '4', '5', '6', '7', '10', '20'].includes(fileType) || directoryType === '3',
    删除: ['1', '2', '3', '4', '5', '6', '7', '10', '20'].includes(fileType) || directoryType === '3',
    // 建模: 设备文件夹 且是目录
    建模: hasPermission('system:file:tag:modeling') && directoryType === '2' && isDir,
    // 共享文件夹目录，status:0: 正常,1：取消，2：过期。 0：显示按钮复制链接、查看二维码、取消分享。1,2：显示按钮删除
    复制链接: fileDirectoryShareInfo?.status === 0,
    取消分享: fileDirectoryShareInfo?.status === 0,
    二维码: fileDirectoryShareInfo?.status === 0,
    删除分享: fileDirectoryShareInfo?.status === 1 || fileDirectoryShareInfo?.status === 2,
  };

  //所属部门的数据操作按钮权限沿用旧逻辑，非所属部门的数据操作按钮隐藏（除下载按钮外）
  if (isMyPart.value) {
    return btnAccess[type];
  } else {
    if (type === '下载') {
      return btnAccess[type];
    }
    return false;
  }
};

function createActions(record: EditRecordRow, column: BasicColumn): ActionItem[] {
  // 先判断是否是所属部门的数据，是则沿用之前的逻辑，否则所有操作都隐藏（除下载按钮外）。
  return [
    {
      label: '下载',
      icon: 'ant-design:download-outlined',
      onClick: handleAction.bind(null, '下载', record),
      ifShow: btnAuth('下载', record),
    },
    {
      label: '重命名',
      icon: 'ant-design:edit-outlined',
      onClick: handleAction.bind(null, '重命名', record),
      ifShow: btnAuth('重命名', record),
    },
    {
      label: '移动',
      icon: 'ic:outline-drive-file-move',
      onClick: handleAction.bind(null, '移动', record),
      ifShow: btnAuth('移动', record),
    },
    {
      label: '删除',
      icon: 'icon-park-outline:delete',
      onClick: handleAction.bind(null, '删除', record),
      ifShow: btnAuth('删除', record),
    },
    {
      label: '建模',
      onClick: handleAction.bind(null, '建模', record),
      ifShow: btnAuth('建模', record),
    },
    {
      label: '复制链接',
      icon: 'ant-design:link-outlined',
      onClick: handleAction.bind(null, '复制链接', record),
      ifShow: btnAuth('复制链接', record),
    },
    {
      label: '取消分享',
      icon: 'ant-design:stop-outlined',
      onClick: handleAction.bind(null, '取消分享', record),
      ifShow: btnAuth('取消分享', record),
    },
    {
      label: '删除分享',
      icon: 'icon-park-outline:delete',
      onClick: handleAction.bind(null, '删除分享', record),
      ifShow: btnAuth('删除分享', record),
    },
    {
      label: '二维码',
      // icon: 'ant-design:qrcode-outlined',
      // onClick: handleAction.bind(null, '二维码', record),
      ifShow: btnAuth('二维码', record),
      slot: 'qrcode',
    },
  ];
}

const resetSearchParams = () => {
  queryParam.isLabel = null;
  queryParam.labelIds = [];
  queryParam.device = '';
  queryParam.fileType = '';
  queryParam.fileName = '';

  queryParam.beginTime = '';
  queryParam.endTime = '';
  dateRange.value = [];
};

// 切换文件夹导航
const clickDir = (index: number) => {
  // 置空搜索条件
  resetSearchParams();

  let navigation = dirNavigation.value;
  navigation = navigation.slice(0, index + 1);
  dirNavigation.value = navigation;
  sessionStorage.setItem(DataDirNavigation, JSON.stringify(navigation));
  console.log('navigation', navigation);
  queryParam.pageNo = 1;
  setPagination({ current: 1 });
  queryParam.parentId = navigation[navigation.length - 1]?.id;
  searchQuery();
  console.log('dirNavigation', dirNavigation.value);
};

// 跳转到子文件夹
const openDir = (record) => {
  //跨目录，清空选中行
  setSelectedRowKeys([]);

  //console.log("子文件夹--record", record)
  const { dirId, directoryName, directoryType } = record;
  dirNavigation.value.push({
    name: directoryName,
    id: dirId,
    dirType: directoryType,
  });
  sessionStorage.setItem(DataDirNavigation, JSON.stringify(dirNavigation.value));
  queryParam.pageNo = 1;
  setPagination({ current: 1 });
  queryParam.parentId = dirId;
  searchQuery();
  console.log('dirNavigation111', dirNavigation.value);
};

// 创建防抖函数（500ms延迟）
const debouncedSearch = debounce(searchQuery, 800);

const selectChange = (type: string, value?: any) => {
  // parentId为''时是全局查询，parentId为'0'时是根目录查询

  switch (type) {
    case 'device':
      queryParam.device = value ?? '';
      break;
    case 'labelIds':
      queryParam.labelIds = value ?? [];
      break;
    case 'isLabel':
      queryParam.isLabel = value ?? null;
      break;
    case 'fileType':
      queryParam.fileType = value ?? '';
      // sessionStorage.setItem(DataSearchCondition, JSON.stringify({ fileType: value }));
      break;
  }
};

watch(
  () => route.path,
  (newPath, oldPath) => {
    console.log(newPath, oldPath, 'newPath,oldPath-----');
    if (newPath == '/dataManage/dataManage' && oldPath == '/dataManage/dataManage/detail') {
      searchQuery();
    }
  }
);
</script>

<style lang="less" scoped>
/deep/ .jeecg-basic-table {
  flex: 1;
  min-width: 0; /* 必须要加，让 flex 子元素能缩 */
  max-width: 100%; /* 防止超出容器 */
  overflow-x: auto;
}
/deep/ .jeecg-basic-table .ant-table-wrapper {
  padding: 16px;
}

/deep/ .cell-content::before {
  content: url('/@/assets/icons/FolderOpenFilled.svg');
  margin-right: 10px;
  vertical-align: middle;
}

/deep/ .cell-content {
  cursor: pointer;
}

/deep/ .ant-table-thead > tr > th {
  background: #edf6fb;
  color: #0f699a;
}

/deep/ .ant-table-tbody > tr.ant-table-row:hover > td,
.ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background: #f7fbfc;
}

/deep/ .ant-popover-buttons {
  display: flex;
}

/deep/ .jeecg-basic-table-header__toolbar {
  width: 1200px;
  margin-right: 0px;
  justify-content: flex-end;
}

/deep/ .jeecg-basic-table-header__toolbar > * {
  margin-right: 0px;
}

/deep/ .ant-table-container {
  height: 580px;
}

/deep/ .jeecg-basic-table-action {
  justify-content: center;
}

.bg {
  display: inline-block;
}
.p-4 {
  padding: 20px;
  :deep(.ant-modal .ant-modal-body) {
    padding: 20px;
  }
  .tableWrap {
    display: flex;
    :deep(.ant-tree) {
      height: 100%;
      width: max-content;
      padding: 20px;
    }
  }
  .upload {
    background: linear-gradient(315deg, #2997d2 0%, #1880b9 100%);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
    border-radius: 2px;
    color: white;
    margin-left: 8px;
  }
  .download {
    background: linear-gradient(315deg, #2997d2 0%, #1880b9 100%);
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
    border-radius: 2px;
    color: white;
    margin-left: 8px;
  }
  .show-in-map {
    margin-left: 8px;
  }
  .toolbar {
    display: flex;
    justify-content: flex-end;
    margin-right: 0px;
    width: 100%;
    .addButton {
      margin-left: 8px;
    }
  }

  .folderLevelBox {
    display: flex;
    margin-top: 9px;
    margin-bottom: 16px;

    .left {
      width: 84px;
      height: 40px;
      background: linear-gradient(180deg, #e8f3f8 0%, rgba(232, 243, 248, 0) 100%);
      opacity: 0.9;
      border: 1px solid rgba(11, 121, 181, 0.6);
      display: flex;
      align-items: center;
      justify-content: center;
      color: #60a7ce;
      font-weight: 400;
      font-size: 14px;
    }

    .right {
      flex: 1;
      height: 40px;
      display: flex;
      align-items: center;
      margin-right: 8px;

      .rightBox {
        width: 100%;
        height: 100%;
        padding-left: 12px;
        display: flex;
        align-items: center;
        background: linear-gradient(180deg, #e8f3f8 0%, rgba(232, 243, 248, 0) 100%);
        opacity: 0.8;
        border: 1px solid rgba(11, 121, 181, 0.6);

        .box {
          display: flex;
          align-items: center;
          margin-right: 4px;

          .img {
            display: flex;
            align-items: center;
          }

          .text {
            margin-left: 6px;
            margin-right: 6px;
            color: #666666;
            line-height: 90%;
            height: 100%;

            &.openDir:hover {
              cursor: pointer;
            }
          }
        }

        .box:last-child {
          .anticon-caret-right:last-child {
            display: none;
          }
        }
      }
    }

    .fileNameInputSearch {
      width: 220px;
      height: 40px;
    }
  }

  .colFileName {
    .openDir:hover {
      cursor: pointer;
    }

    .dirIcon {
      width: 30px;
      height: 26px;
      margin-right: 5px;

      > img {
        width: 100%;
        height: 100%;
      }
    }

    .fileInfoWrap {
      display: flex;
      align-items: center;

      .fileIcon {
        // width: 30px;
        height: 26px;
        margin-right: 10px;

        > img {
          height: 100%;
        }
      }
    }
  }
}
.progress-result {
  display: flex;
  align-items: center;
  justify-content: center;
  .result-text {
    margin-right: 6px;
  }
}
.notMark {
  display: inline-block;
  width: fit-content;
  padding: 0 10px;
  background: rgba(179, 21, 21, 0.1);
  border-radius: 2px;
  border: 1px solid #f3b3b3;
  color: #d95e5e;
}
.allMark {
  display: inline-block;
  width: fit-content;
  padding: 0 10px;
  background: rgba(21, 179, 122, 0.1);
  border-radius: 2px;
  border: 1px solid #85ccb2;
  color: #49a282;
}
.partMark {
  display: inline-block;
  width: fit-content;
  padding: 0 10px;
  background: rgba(70, 122, 206, 0.1);
  border-radius: 2px;
  border: 1px solid #b3c8f3;
  color: #467ace;
}
.waitMark {
  display: inline-block;
  width: fit-content;
  padding: 0 10px;
  background: rgba(255, 134, 49, 0.1);
  border-radius: 2px;
  border: 1px solid rgba(255, 134, 49, 0.5);
  color: #ff8631;
}
</style>

<style lang="less" scoped>
.ant-input-group-wrapper.fileNameInputSearch {
  .ant-input-group-addon {
    .ant-btn.ant-input-search-large.ant-input-search-button {
      height: 40.5px;
    }
  }
}
</style>
