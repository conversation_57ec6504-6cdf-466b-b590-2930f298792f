<template>
  <!-- <Conatiner :options="{ width: winWdt, height: winHit - 80 }"> -->
  <div class="homePageBox" id="homePageID" ref="dataScreenRef" :style="{ width: winWdt + 'px', height: winHit - 80 + 'px' }">
    <!-- <div class="headBox" :style="{ backgroundImage: 'url(' + jobImgUrl('表头_01') + ')', backgroundColor: '#05070E' }">
        <div class="numberProjectBox">
          <div class="Icon" @click="exitFullScreen">
            <img :src="jobImgUrl('项目数量', 'svg')" alt="" />
          </div>
          <div class="text" @click="enterFullScreen"> 项目数量：1 </div>
        </div>
        <div class="title">
          {{ homePageTitle }}
        </div>
        <div class="headTime">
          {{ getTime }}
        </div>
      </div> -->

    <div class="mapBox" :style="{ backgroundImage: 'url(' + jobImgUrl('背景图', 'jpg') + ')' }">
      <div class="homePageLeft">
        <div class="equipInform">
          <div class="treeSelectBox">
            <a-tree-select
              v-model:value="selectedDepartment"
              multiple
              show-search
              tree-default-expand-all
              style="min-width: 181px; max-width: auto; margin-right: 8px"
              :dropdown-style="{ width: 'auto' }"
              :tree-data="departmentList"
              :field-names="{ children: 'children', label: 'departName', value: 'orgCode' }"
              :dropdownMatchSelectWidth="false"
              placeholder="请选择部门"
              :maxTagCount="1"
              :maxTagPlaceholder="(omittedValues) => `+${omittedValues.length} 更多`"
              @change="selectChange"
            >
              <template #title="{ value: val, departName }" style="width: 500px">
                <div>{{ departName }}</div>
              </template>
            </a-tree-select>
          </div>
          <div class="head">
            <div class="icon">
              <img :src="jobImgUrl('设备信息', 'svg')" alt="" />
            </div>
            <div class="text"> 设备信息 </div>
          </div>
          <div class="numberDevices">
            <div class="left">
              <div class="numberDevice-left">
                <Progress type="circle" :percent="70" size="small">
                  <template #format>
                    <img :src="jobImgUrl('设备信息-机场', 'svg')" alt="" :style="{ marginTop: '-10px' }" />
                  </template>
                </Progress>
                <div class="quantity">
                  <div class="Titel">机场数</div>
                  <div class="number">{{ equipInform?.dockStatsVo.num || 0 }}/{{ equipInform?.dockStatsVo.totalNum || 0 }} </div>
                </div>

                <div class="quantity">
                  <div class="Titel">作业</div>
                  <div class="number">{{ equipInform?.dockStatsVo.workNum || 0 }}</div>
                </div>

                <div class="quantity">
                  <div class="Titel">空闲</div>
                  <div class="number">{{ equipInform?.dockStatsVo.spareNum || 0 }}</div>
                </div>

                <!-- <div class="quantity">
                <div class="Titel">调试</div>
                <div class="number">{{ equipInform?.dockStatsVo.debugNum || 0 }}</div>
              </div>

              <div class="quantity">
                <div class="Titel">固件升级</div>
                <div class="number">{{ equipInform?.dockStatsVo.upgradeNum || 0 }}</div>
              </div> -->
              </div>
            </div>
            <div class="right">
              <div class="numberDeviceTwo-right">
                <Progress type="circle" style="position: relative" :percent="70" size="small">
                  <template #format>
                    <img style="position: absolute; top: -11px; left: 12px" :src="jobImgUrl('设备信息-无人机', 'svg')" alt="" />
                  </template>
                </Progress>
                <div class="quantity">
                  <div class="Titel">无人机数</div>
                  <div class="number">{{ equipInform?.droneStatsVo.num || 0 }}/{{ equipInform?.droneStatsVo.totalNum || 0 }}</div>
                </div>

                <div class="quantity">
                  <div class="Titel">作业</div>
                  <div class="number">{{ equipInform?.droneStatsVo.workNum }}</div>
                </div>

                <div class="quantity">
                  <div class="Titel">待机</div>
                  <div class="number">{{ equipInform?.droneStatsVo.spareNum }}</div>
                </div>
              </div>
            </div>
          </div>
          <!-- <div class="numberDevice">
              <Progress type="circle" :percent="70" size="small">
                <template #format>
                  <img :src="jobImgUrl('飞机', 'svg')" alt="" :style="{ marginTop: '-10px' }" />
                </template>
              </Progress>
              <div class="quantity">
                <div class="Titel">机场数</div>
                <div class="number">{{ equipInform?.dockStatsVo.num || 0 }}/{{ equipInform?.dockStatsVo.totalNum || 0 }} </div>
              </div>

              <div class="quantity">
                <div class="Titel">作业</div>
                <div class="number">{{ equipInform?.dockStatsVo.workNum || 0 }}</div>
              </div>

              <div class="quantity">
                <div class="Titel">空闲</div>
                <div class="number">{{ equipInform?.dockStatsVo.spareNum || 0 }}</div>
              </div>

              <div class="quantity">
                <div class="Titel">调试</div>
                <div class="number">{{ equipInform?.dockStatsVo.debugNum || 0 }}</div>
              </div>

              <div class="quantity">
                <div class="Titel">固件升级</div>
                <div class="number">{{ equipInform?.dockStatsVo.upgradeNum || 0 }}</div>
              </div>
            </div>
            <div class="numberDeviceTwo">
              <Progress type="circle" :percent="70" size="small">
                <template #format>
                  <img :src="jobImgUrl('无人机', 'svg')" alt="" />
                </template>
              </Progress>
              <div class="quantity" style="margin-left: -39px">
                <div class="Titel">无人机数</div>
                <div class="number">{{ equipInform?.droneStatsVo.num || 0 }}/{{ equipInform?.droneStatsVo.totalNum || 0 }}</div>
              </div>

              <div class="quantity">
                <div class="Titel">作业</div>
                <div class="number">{{ equipInform?.droneStatsVo.workNum }}</div>
              </div>

              <div class="quantity">
                <div class="Titel">待机</div>
                <div class="number">{{ equipInform?.droneStatsVo.spareNum }}</div>
              </div>
            </div> -->
        </div>
        <div class="dataStatist">
          <div class="head">
            <div class="icon">
              <img :src="jobImgUrl('数据统计', 'svg')" alt="" />
            </div>
            <div class="text"> 数据统计-媒体 </div>
          </div>
          <div class="totalData">
            <div class="total">总数据量：{{ totalData || 0 }}</div>
            <!-- <div class="today">今日 +15</div>
              <img :src="jobImgUrl('箭头', 'svg')" alt="" /> -->
          </div>
          <Bar width="399px" height="180px" ref="BarRef" :option="chartConfigure" :chartData="chartData"></Bar>
        </div>
        <div class="dataStatist">
          <div class="head">
            <div class="icon">
              <img :src="jobImgUrl('数据统计', 'svg')" alt="" />
            </div>
            <div class="text"> 标签 </div>
          </div>
          <Bar width="26.5rem" height="180px" ref="BarRef" :option="chartConfigure" :chartData="markCategory"></Bar>
          <!-- <div class="dotBox">
            <div
              class="dot"
              :class="{ dotBack: index == markCategoryIndex }"
              v-for="(item, index) in markCategoryClassify"
              @click="toggleLabelData(item, index)"
            ></div>
          </div> -->
        </div>
        <div class="alarm">
          <div class="head">
            <div class="icon">
              <img :src="jobImgUrl('告警', 'svg')" alt="" />
            </div>
            <div class="text"> 告警 </div>
          </div>
          <div style="flex: 1; display: flex; align-items: center" :class="AlarmStats?.length > 1 ? 'AlarmStatsCenter' : ''">
            <div class="alarmInformBox" :style="{ height: winHit - 740 > 179 ? Math.abs(winHit - 740) + 'px' : '11rem' }">
              <a-row :gutter="[1, 11]">
                <a-col :span="12" v-for="item in AlarmStats">
                  <div class="alarmInform">
                    <div class="alarmInformTop">
                      <div class="Icon">
                        <img :src="jobImgUrl('机场', 'svg')" />
                      </div>
                      <div class="titleBox">
                        <div class="title">{{ item.deviceName }}</div>
                        <div class="time">{{ item.startTime }}</div>
                      </div>
                      <div class="status">告警</div>
                    </div>
                    <div class="alarmInformBot" :title="item.alarmContent">告警：{{ item.alarmContent }}</div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
        </div>
      </div>
      <homeMap @marsOnload="marsOnload" :options="lat" @info="info"> </homeMap>
      <div class="BottomQuantity">
        <div class="DateSwitching" v-if="false">
          <div class="toggleButton" :class="{ toggleButtonOptfor: SelectedType === 'day' }" @click="toggleButton('day')">今日 </div>
          <div class="toggleButton" :class="{ toggleButtonOptfor: SelectedType === 'week' }" @click="toggleButton('week')">本周 </div>
          <div class="toggleButton" :class="{ toggleButtonOptfor: SelectedType === 'month' }" @click="toggleButton('month')">本月 </div>
          <div class="toggleButton" :class="{ toggleButtonOptfor: SelectedType === 'all' }" @click="toggleButton('all')">累计 </div>
        </div>
        <div class="QuantityBox" v-if="false">
          <div class="statisticsBox">
            <div class="top">
              <img class="uavImg" :src="jobImgUrl('无人机', 'svg')" alt="" />
              <div class="number">25</div>
            </div>
            <div class="text">在线数量</div>
          </div>

          <div class="statisticsBox">
            <div class="top">
              <img class="uavImg" :src="jobImgUrl('飞机', 'svg')" alt="" />
              <div class="number">25</div>
            </div>
            <div class="text">离线数量</div>
          </div>

          <div class="statisticsBox">
            <div class="top">
              <img class="uavImg" :src="jobImgUrl('无人机', 'svg')" alt="" />
              <div class="number">25</div>
            </div>
            <div class="text">在线数量</div>
          </div>

          <div class="statisticsBox">
            <div class="top">
              <img class="uavImg" :src="jobImgUrl('飞机', 'svg')" alt="" />
              <div class="number">25</div>
            </div>
            <div class="text">离线数量</div>
          </div>
        </div>
      </div>
      <div class="homePageRight">
        <div class="realTimVideo">
          <div class="head">
            <div class="headNoe">
              <div class="icon">
                <img :src="jobImgUrl('实时视频', 'svg')" alt="" />
              </div>
              <div class="text"> 实时视频 </div>
            </div>
            <!-- <div class="LiveSwitchingBox">
                <div class="Switch"></div>
              </div> -->
            <div class="headTwo">
              <div class="headBot" @click="VideoButton(true)">
                <caret-right-outlined />
                播放
              </div>

              <div class="headBot" @click="VideoButton(false)" style="margin-right: 0.6875rem">
                <pause-outlined />
                暂停
              </div>
              <!-- <img :src="jobImgUrl('全屏', 'svg')" @click="navToFullVideos()" alt="" /> -->
            </div>
          </div>
          <div class="realTimVideoBox">
            <div class="videoBox">
              <div style="height: 200px; display: flex; align-items: center; justify-content: center" v-if="LiveAirport.length == 0">
                <div style="color: #ffffff">无设备在线</div>
              </div>
              <div class="screenNoe" v-if="LiveAirport.length == 1">
                <div class="NoeBox">
                  <Jessibuca
                    v-if="Object.keys(LiveAirport[0].LiveList).length != 0"
                    :urlType="LiveAirport[0].LiveList.urlType"
                    :videoUrl="LiveAirport[0].LiveList.videoUrl"
                    :appId="LiveAirport[0].LiveList.appId"
                    :channel="LiveAirport[0].LiveList.channel"
                    :token="LiveAirport[0].LiveList.token"
                  />
                  <div :class="Object.keys(LiveAirport[0].LiveList).length > 0 && LiveAirport[0].info.liveStreaming ? 'WaitPlayHover' : 'WaitPlay'">
                    <img
                      v-show="Object.keys(LiveAirport[0].LiveList).length == 0"
                      @click="StartLiveClick(LiveAirport[0], 0)"
                      :src="jobImgUrl('播放', 'svg')"
                    />

                    <img
                      v-show="Object.keys(LiveAirport[0].LiveList).length != 0 && LiveAirport[0].info.liveStreaming"
                      @click="closeLiveClick(LiveAirport[0], 0)"
                      :src="jobImgUrl('播放中', 'svg')"
                    />
                  </div>
                </div>
                <div class="NoeTitle">
                  <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper"
                    ><span class="ant-badge-status-dot" style="background: rgb(55, 252, 182)"></span
                    ><span class="ant-badge-status-text" style="color: #38fcb6; font-size: 12px">{{ LiveAirport[0].info.deviceName }}</span></span
                  >
                </div>
              </div>
              <div class="screenTwo" v-if="LiveAirport.length == 2">
                <div class="TwoBox" v-for="(item, index) in LiveAirport">
                  <div class="TwoVideo">
                    <Jessibuca
                      v-if="Object.keys(item.LiveList).length != 0"
                      :urlType="item.LiveList.urlType"
                      :videoUrl="item.LiveList.videoUrl"
                      :appId="item.LiveList.appId"
                      :channel="item.LiveList.channel"
                      :token="item.LiveList.token"
                    />
                    <div :class="Object.keys(item.LiveList).length > 0 && item.info.liveStreaming ? 'WaitPlayHover' : 'WaitPlay'">
                      <img v-show="Object.keys(item.LiveList).length == 0" @click="StartLiveClick(item, index)" :src="jobImgUrl('播放', 'svg')" />
                      <img
                        v-show="Object.keys(item.LiveList).length != 0 && item.info.liveStreaming"
                        @click="closeLiveClick(item, index)"
                        :src="jobImgUrl('播放中', 'svg')"
                      />
                    </div>
                  </div>
                  <div class="TwoTitle">
                    <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper"
                      ><span class="ant-badge-status-dot" style="background: rgb(55, 252, 182)"></span
                      ><span class="ant-badge-status-text" style="color: #38fcb6; font-size: 12px">{{ item.info.deviceName }}</span></span
                    >
                  </div>
                </div>
              </div>
              <div class="screenThree" v-if="LiveAirport.length == 3">
                <div class="ThreeLeft">
                  <div class="LeftVideo">
                    <Jessibuca
                      v-if="Object.keys(LiveAirport[0].LiveList).length != 0"
                      :urlType="LiveAirport[0].LiveList.urlType"
                      :videoUrl="LiveAirport[0].LiveList.videoUrl"
                      :appId="LiveAirport[0].LiveList.appId"
                      :channel="LiveAirport[0].LiveList.channel"
                      :token="LiveAirport[0].LiveList.token"
                    />
                    <div :class="Object.keys(LiveAirport[0].LiveList).length > 0 && LiveAirport[0].info.liveStreaming ? 'WaitPlayHover' : 'WaitPlay'">
                      <img
                        v-show="Object.keys(LiveAirport[0].LiveList).length == 0"
                        @click="StartLiveClick(LiveAirport[0], 0)"
                        :src="jobImgUrl('播放', 'svg')"
                      />

                      <img
                        v-show="Object.keys(LiveAirport[0].LiveList).length != 0 && LiveAirport[0].info.liveStreaming"
                        @click="closeLiveClick(LiveAirport[0], 0)"
                        :src="jobImgUrl('播放中', 'svg')"
                      />
                    </div>
                  </div>
                  <div class="LeftTitle">
                    <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper"
                      ><span class="ant-badge-status-dot" style="background: rgb(55, 252, 182)"></span
                      ><span class="ant-badge-status-text" style="color: #38fcb6; font-size: 12px">{{ LiveAirport[0].info.deviceName }}</span></span
                    >
                  </div>
                </div>
                <div class="Threeright">
                  <div class="rightTop">
                    <div class="TopVideo">
                      <Jessibuca
                        v-if="Object.keys(LiveAirport[1].LiveList).length != 0"
                        :urlType="LiveAirport[1].LiveList.urlType"
                        :videoUrl="LiveAirport[1].LiveList.videoUrl"
                        :appId="LiveAirport[1].LiveList.appId"
                        :channel="LiveAirport[1].LiveList.channel"
                        :token="LiveAirport[1].LiveList.token"
                      />

                      <div
                        :class="Object.keys(LiveAirport[1].LiveList).length > 0 && LiveAirport[1].info.liveStreaming ? 'WaitPlayHover' : 'WaitPlay'"
                      >
                        <img
                          v-show="Object.keys(LiveAirport[1].LiveList).length == 0"
                          @click="StartLiveClick(LiveAirport[1], 1)"
                          :src="jobImgUrl('播放', 'svg')"
                        />

                        <img
                          v-show="Object.keys(LiveAirport[1].LiveList).length != 0 && LiveAirport[1].info.liveStreaming"
                          @click="closeLiveClick(LiveAirport[1], 1)"
                          :src="jobImgUrl('播放中', 'svg')"
                        />
                      </div>
                    </div>
                    <div class="TopTitle">
                      <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper"
                        ><span class="ant-badge-status-dot" style="background: rgb(55, 252, 182)"></span
                        ><span class="ant-badge-status-text" style="color: #38fcb6; font-size: 12px">{{ LiveAirport[1].info.deviceName }}</span></span
                      >
                    </div>
                  </div>
                  <div class="rightBot">
                    <div class="BotVideo">
                      <Jessibuca
                        v-if="Object.keys(LiveAirport[2].LiveList).length != 0"
                        :urlType="LiveAirport[2].LiveList.urlType"
                        :videoUrl="LiveAirport[2].LiveList.videoUrl"
                        :appId="LiveAirport[2].LiveList.appId"
                        :channel="LiveAirport[2].LiveList.channel"
                        :token="LiveAirport[2].LiveList.token"
                      />

                      <div
                        :class="Object.keys(LiveAirport[2].LiveList).length > 0 && LiveAirport[2].info.liveStreaming ? 'WaitPlayHover' : 'WaitPlay'"
                      >
                        <img
                          v-show="Object.keys(LiveAirport[2].LiveList).length == 0"
                          @click="StartLiveClick(LiveAirport[2], 2)"
                          :src="jobImgUrl('播放', 'svg')"
                        />

                        <img
                          v-show="Object.keys(LiveAirport[2].LiveList).length != 0 && LiveAirport[2].info.liveStreaming"
                          @click="closeLiveClick(LiveAirport[2], 2)"
                          :src="jobImgUrl('播放中', 'svg')"
                        />
                      </div>
                    </div>
                    <div class="BotTitle">
                      <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper"
                        ><span class="ant-badge-status-dot" style="background: rgb(55, 252, 182)"></span
                        ><span class="ant-badge-status-text" style="color: #38fcb6; font-size: 12px">{{ LiveAirport[2].info.deviceName }}</span></span
                      >
                    </div>
                  </div>
                </div>
              </div>
              <a-row :gutter="[11, 8]" v-if="LiveAirport.length == 4">
                <a-col :span="12" v-for="(item, index) in LiveAirport">
                  <div class="screenMore">
                    <div class="MorsTop">
                      <div class="videoBox">
                        <Jessibuca
                          v-if="Object.keys(item.LiveList).length != 0"
                          :urlType="item.LiveList.urlType"
                          :videoUrl="item.LiveList.videoUrl"
                          :appId="item.LiveList.appId"
                          :channel="item.LiveList.channel"
                          :token="item.LiveList.token"
                        />

                        <div :class="Object.keys(item.LiveList).length > 0 && item.info.liveStreaming ? 'WaitPlayHover' : 'WaitPlay'">
                          <img v-show="Object.keys(item.LiveList).length == 0" @click="StartLiveClick(item, index)" :src="jobImgUrl('播放', 'svg')" />
                          <img
                            v-show="Object.keys(item.LiveList).length != 0 && item.info.liveStreaming"
                            @click="closeLiveClick(item, index)"
                            :src="jobImgUrl('播放中', 'svg')"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="MorsBot">
                      <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper"
                        ><span class="ant-badge-status-dot" style="background: rgb(55, 252, 182)"></span
                        ><span class="ant-badge-status-text" style="color: #38fcb6; font-size: 12px">{{ item.info.deviceName }}</span></span
                      >
                    </div>
                  </div>
                </a-col>
              </a-row>
              <a-row :gutter="[11, 8]" v-if="LiveAirport.length > 4">
                <a-col :span="8" v-for="(item, index) in LiveAirport">
                  <div class="screenMulti">
                    <div class="MorsTop">
                      <div class="videoBox">
                        <Jessibuca
                          v-if="Object.keys(item.LiveList).length != 0"
                          :urlType="item.LiveList.urlType"
                          :videoUrl="item.LiveList.videoUrl"
                          :appId="item.LiveList.appId"
                          :channel="item.LiveList.channel"
                          :token="item.LiveList.token"
                        />

                        <div :class="Object.keys(item.LiveList).length > 0 && item.info.liveStreaming ? 'WaitPlayHover' : 'WaitPlay'">
                          <img v-show="Object.keys(item.LiveList).length == 0" @click="StartLiveClick(item, index)" :src="jobImgUrl('播放', 'svg')" />
                          <img
                            v-show="Object.keys(item.LiveList).length != 0 && item.info.liveStreaming"
                            @click="closeLiveClick(item, index)"
                            :src="jobImgUrl('播放中', 'svg')"
                          />
                        </div>
                      </div>
                    </div>
                    <div class="MorsBot">
                      <span class="ant-badge ant-badge-status ant-badge-not-a-wrapper"
                        ><span class="ant-badge-status-dot" style="background: rgb(55, 252, 182)"></span
                        ><span class="ant-badge-status-text" style="color: #38fcb6; font-size: 12px">{{ item.info.deviceName }}</span></span
                      >
                    </div>
                  </div>
                </a-col>
              </a-row>
            </div>
          </div>
          <div class="realTimVideoBox">
            <a-row :gutter="[11, 8]">
              <!-- <a-col :span="8">
	              <div class="videoBox">
	                <div class="videoName">
	                  <img :src="jobImgUrl('MT30', 'svg')" alt="">
	                  <div class="text">KTU-005</div>
	                </div>
	                <div class="video">
	                  <Jessibuca :urlType="videoOption.urlType" :videoUrl="videoOption.url" :appId="videoOption.appId"
	                    :channel="videoOption.channel" :token="videoOption.token" />
	                </div>
	                <div class="textBox">
	                  <a-badge color="#37fcb6" text="机场监控" />
	                </div>
	              </div>
	            </a-col> -->
              <!-- <a-col :span="8" v-for="i in 6">
                  <div class="videoBox">
                    <div class="videoName">
                      <img :src="jobImgUrl('MT30', 'svg')" alt="" />
                      <div class="text">KTU-005</div>
                    </div>
                    <div class="video">
                      <Jessibuca
                        v-if="i < videoOptions.length"
                        :urlType="videoOptions[i].urlType"
                        :videoUrl="videoOptions[i].liveUrl ? videoOptions[i].liveUrl.replace('rtmp://', 'ws://') + '.live.flv' : ''"
                      />
                    </div>
                    <div class="textBox">
                      <a-badge color="#37fcb6" :text="i < videoOptions.length ? videoOptions[i].gatewayDeviceSn : ''" />
                    </div>
                  </div>
                </a-col> -->
            </a-row>
          </div>
        </div>

        <div class="taskStatistBox">
          <div class="head">
            <div class="icon">
              <img :src="jobImgUrl('任务统计', 'svg')" alt="" />
            </div>
            <div class="text"> 任务统计 </div>
          </div>
          <div class="statistInform">
            <div class="chartBox">
              <Pie width="200px" height="200px" :option="pieChartOption" ref="pieChartRef" :chartData="pieChartChartData"></Pie>
              <div class="task">
                <div class="number">{{ taskStatisticsList?.allTask }}</div>
                <div class="text">全部任务</div>
              </div>
            </div>
            <div class="Inform">
              <!-- <div class="rise">
                  <div class="title">今日上涨</div>
                  <div class="text">{{ taskStatisticsList?.todayRisePercent }}</div>
                  <img :src="jobImgUrl('箭头', 'svg')" alt="" />
                </div> -->
              <div class="completeBox">
                <div class="complete">
                  <div class="boxOne">
                    <div class="block"></div>
                    <div class="text">已完成</div>
                  </div>
                  <div class="bixTwo">
                    <div class="number">{{ taskStatisticsList?.completeTask }}</div>
                    <div class="percentage">{{ taskStatisticsList?.completeTaskPercent }}</div>
                  </div>
                </div>

                <div class="complete">
                  <div class="boxOne">
                    <div class="black"></div>
                    <div class="text">待执行</div>
                  </div>
                  <div class="bixTwo">
                    <div class="number">{{ taskStatisticsList?.waitExecuteTask }}</div>
                    <div class="percentage">{{ taskStatisticsList?.waitExecuteTaskPercent }}</div>
                  </div>
                </div>

                <div class="complete">
                  <div class="boxOne">
                    <div class="red"></div>
                    <div class="text">失败</div>
                  </div>
                  <div class="bixTwo">
                    <div class="number">{{ taskStatisticsList?.failedTask }}</div>
                    <div class="percentage">{{ taskStatisticsList?.failedTaskPercent }}</div>
                  </div>
                </div>

                <div class="complete">
                  <div class="boxOne">
                    <div class="zxz"></div>
                    <div class="text">执行中</div>
                  </div>
                  <div class="bixTwo">
                    <div class="number">{{ taskStatisticsList?.inExecuteTask }}</div>
                    <div class="percentage">{{ taskStatisticsList?.inExecuteTaskPercent }}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="flightStatist">
          <div class="head">
            <div class="icon">
              <img :src="jobImgUrl('飞行统计-icon', 'svg')" alt="" />
            </div>
            <div class="text"> 飞行统计 </div>
          </div>

          <div class="flightTimeBox">
            <div class="flightTime">
              <img :src="jobImgUrl('行时', 'svg')" alt="" />
              <div class="flightBox">
                <div class="time">{{ (flightStatistics?.totalFlightTime / 3600).toFixed(2) }}</div>
                <div class="text">航时(h)</div>
              </div>
            </div>

            <div class="flightTime">
              <img :src="jobImgUrl('架次', 'svg')" alt="" />
              <div class="flightBox">
                <div class="time">{{ flightStatistics?.totalFlightSorties }}</div>
                <div class="text">架次</div>
              </div>
            </div>

            <div class="flightTime">
              <img :src="jobImgUrl('里程', 'svg')" alt="" />
              <div class="flightBox">
                <div class="time">{{ (flightStatistics?.totalFlightDistance / 1000).toFixed(2) }}</div>
                <div class="text">里程(km)</div>
              </div>
            </div>
          </div>

          <div class="carouselListBox">
            <div class="meterHead">
              <div class="title">设备</div>
              <div class="title">任务</div>
              <div class="title">航线</div>
              <div class="title">时间</div>
            </div>
            <div style="flex: 1; display: flex; justify-content: center; align-items: center">
              <div class="scrollDataBox" :style="{ height: winHit - 815 > 120 ? Math.abs(winHit - 815) + 'px' : '115px' }">
                <div class="scrollData" v-for="(item, index) in TaskFlightStatisticsList[0]">
                  <div class="title">{{ item.deviceName }}</div>
                  <div class="title">{{ item.taskName }}</div>
                  <div class="title">{{ item.waylineName }}</div>
                  <div class="title">{{ item.createTime }}</div>
                </div>
                <!-- <div class="scrollData">
                  <div class="title">机场</div>
                  <div class="title">任务222</div>
                  <div class="title">日常巡检航线</div>
                  <div class="title">今天 09:22</div>
                </div> -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
  <!-- </Conatiner> -->
</template>
<script lang="ts" name="homePage-index" setup>
  import { ref, onUnmounted, onMounted, reactive } from 'vue';
  import { getIconUrl } from '/@/utils';
  import { Progress } from 'ant-design-vue';
  import Bar from '/@/components/chart/Bar.vue';
  import Pie from '/@/components/chart/Pie.vue';
  import { startLive, shadowGet, stopLive } from '/@/views/remoteOver/data.api.ts';
  import Jessibuca from '@/views/components/Jessibuca/index.vue';
  import { mediaFile } from './data.api';
  import { FullLivingApi } from './data.api';
  import { deviceApi } from '../equipManage/deviceManageList/data.api';
  import { useGlobSetting } from '/@/hooks/setting';
  import homeMap from './homeMap.vue';
  import { list } from '/@/views/projectManage/projectManage/data.api';
  import Conatiner from './Conatiner.vue';
  import { queryById } from '/@/views/projectManage/projectManage/data.api';

  import { useRouter, useRoute } from 'vue-router';

  import { CaretRightOutlined, PauseOutlined } from '@ant-design/icons-vue';
  const router = useRouter();
  function navToFullVideos() {
    if (document.body.requestFullscreen) {
      document.body.requestFullscreen();
    } else if (document.body.webkitRequestFullscreen) {
      document.body.webkitRequestFullscreen();
    } else if (document.body.mozRequestFullScreen) {
      document.body.mozRequestFullScreen();
    } else if (document.body.msRequestFullscreen) {
      document.body.msRequestFullscreen();
    }

    console.log('跳转/fullVideos');

    router.push({ path: '/fullVideos', query: {} });
  }

  const pieChartRef = ref();
  const BarRef = ref();
  let getTime = ref();
  let hourMinuteSecond = ref();
  let chartData = ref([]);
  let totalData = ref(); //总数据量
  let equipInform = ref(); //设备信息
  const { title } = useGlobSetting();

  let pieChartOption = ref({
    tooltip: {
      trigger: 'item',
    },
    // legend: {
    //   top: '5%',
    //   left: 'center'
    // },
    series: [
      {
        name: '任务统计',
        type: 'pie',
        radius: ['50%', '75%'],
        avoidLabelOverlap: false,
        label: {
          show: false,
          position: 'center',
        },
        clockwise: false, // 设置为逆时针
        emphasis: {
          // label: {
          //   show: true,
          //   fontSize: 40,
          //   fontWeight: 'bold'
          // }
        },
        labelLine: {
          show: false,
        },
      },
    ],
  });

  let pieChartChartData = ref([
    {
      value: 0,
      name: '已完成',
      itemStyle: {
        color: {
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#359977' },
            { offset: 1, color: '#54FFE2' },
          ],
        },
      },
    },
    {
      value: 0,
      name: '失败',
      itemStyle: {
        color: {
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#B61414' },
            { offset: 1, color: '#FF6767' },
          ],
        },
      },
    },
    {
      value: 0,
      name: '待执行',
      itemStyle: { color: '#1d2f4d' },
    },
    {
      value: 0,
      name: '执行中',
      itemStyle: {
        color: {
          x: 0,
          y: 0,
          x2: 0,
          y2: 1,
          colorStops: [
            { offset: 0, color: '#65A0FC' },
            { offset: 1, color: '#224C6A' },
          ],
        },
      },
    },
  ]);

  const chartConfigure = {
    yAxis: {
      show: false, // 将左边的Y轴隐藏
    },
    series: [
      {
        type: 'bar',
        label: {
          color: '#ffffff',
          show: true, // 启用标签显示
          position: 'top', // 位置可以是 'top'、'insideTop'、'inside' 等
        },
        itemStyle: {
          color: {
            type: 'linear', // 指定渐变类型
            x: 0, // 渐变起点 x 坐标
            y: 0, // 渐变起点 y 坐标
            x2: 0, // 渐变终点 x 坐标
            y2: 1, // 渐变终点 y 坐标
            colorStops: [
              {
                offset: 0,
                color: '#33d9fa', // 渐变起点颜色
              },
              {
                offset: 0.5,
                color: '#33d9fa98',
              },
              {
                offset: 1,
                color: '#33d9fa36', // 渐变终点颜色
              },
            ],
          },
        },
      },
    ],
  };
  let videoOptions = ref([]); // { url: '', urlType: '', appId: '', channel: '', token: '' }
  let homePageTitle = ref();
  let lat = ref();
  let selectedDepartment = ref(); //选择的部门
  let lastValidValue = <any>[];
  let departmentList = ref(); // 部门列表
  let flightStatistics = ref(); //飞机统计
  let AlarmStats = ref(); //告警统计

  let LiveStreamList = ref([]); //直播流列表

  let LiveAirport = ref([]); //直播流机场监控

  let SelectedType = ref('day'); //选中的类型
  let markCategory = ref();
  let markCategoryIndex = ref();
  let markCategoryClassify = ref();
  let TaskFlightStatisticsList = ref([]);

  let Map = null;

  let taskStatisticsList = ref();
  let taskFlightParams = reactive({
    pageNo: 1,
    pageSize: 100,
    taskIndexStatisticsType: 'day',
  });

  const jobImgUrl = (name: string, suffix = 'png') => {
    return getIconUrl(name, 'icons/homePage/', suffix);
  };
  const png = (name) => {
    return `/mapView/${name}.png`; //拼接文件路径
  };
  let time: ReturnType<typeof setInterval>;
  function getCurrentDateTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = ('0' + (now.getMonth() + 1)).slice(-2);
    const date = ('0' + now.getDate()).slice(-2);
    const hours = ('0' + now.getHours()).slice(-2);
    const minutes = ('0' + now.getMinutes()).slice(-2);
    const seconds = ('0' + now.getSeconds()).slice(-2);
    const days = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
    const day = now.getDay();
    hourMinuteSecond.value = `${hours}:${minutes}:${seconds}`;
    return `${year}年${month}月${date}日 ${hours}:${minutes}:${seconds} ${days[day]}`;
  }

  time = setInterval(() => {
    getTime.value = getCurrentDateTime();
  }, 1000);

  async function startLiveBroadcast() {
    FullLivingApi.queryLiveList().then(function (res) {
      videoOptions.value.push(
        ...res.filter(function (item) {
          return item.urlType == 1;
        })
      );
    });

    // if (!DockShow.cameraId) {
    //   return;
    // }

    // let liveParams = {
    //   gatewayDeviceSn: DockShow.deviceSn,
    //   deviceId: DockShow.deviceId,
    //   deviceSn: DockShow.deviceSn,
    //   cameraId: DockShow.cameras[0].camera_index,
    //   videoIndex: DockShow.cameras[0].video_list[0].video_index
    // };

    // await startLive(liveParams).then(res => {
    //   if (!("urlType" in res)) {
    //     return;
    //   }
    //   videoOption.value.urlType = res.urlType;
    //   if (res.urlType == '0') {
    //     videoOption.value.url = null;
    //     videoOption.value.appId = res.livePlayAgoraUrl.appid;
    //     videoOption.value.channel = res.livePlayAgoraUrl.channel;
    //     videoOption.value.token = res.livePlayAgoraUrl.token;
    //   } else if (res.urlType == '1') {
    //     res.livePalyUrl && (videoOption.value.url = res.livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv'));
    //   }
    //   // console.log(res)
    // });
  }

  async function getData() {
    await deviceApi
      .list({
        pageSize: 10,
        pageNo: 1,
        deviceType: '3',
        projectId: localStorage.getItem('Project_Id'),
        sysMultiOrgCode: selectedDepartment.value.join(','),
        deptQueryType: 'MULTIPLE',
      })
      .then(function (res) {
        lat.value = res.records;
        let docklist = res.records.filter(function (item) {
          return item.state != 99;
        });
        if (!docklist || docklist.length == 0) {
          return;
        }

        console.log(lat.value, 'lat.value-------------------------------------');
        let dock = docklist[0];
        if (dock.liveCapacity && dock.liveCapacity.coexist_video_number_max > 0) {
          DockShow.deviceId = dock.deviceId;
          DockShow.deviceName = dock.deviceName;
          DockShow.deviceSn = dock.deviceSn;
          DockShow.deviceType = dock.deviceType;
          DockShow.deviceState = dock.state;
          if (dock.deviceList.length > 0) {
            DockShow.droneId = dock.deviceList[0].deviceId;
            DockShow.droneName = dock.deviceList[0].deviceName;
            DockShow.droneSn = dock.deviceList[0].deviceSn;
            DockShow.droneState = dock.deviceList[0].state;
            DockShow.droneModel = dock.deviceList[0].deviceModel;
          }
          DockShow.cameras = [];
          DockShow.droneCameras = [];

          var device_list = dock.liveCapacity.device_list;
          for (var i in device_list) {
            var cameralist = device_list[i].camera_list;
            for (var j in cameralist) {
              if (device_list[i].sn == dock.deviceSn) {
                DockShow.cameras.push(cameralist[j]);
              } else {
                DockShow.droneCameras.push(cameralist[j]);
              }
            }
          }
        }
        LiveAirport.value = [];
        docklist.forEach((item, index) => {
          let params = {
            params: {
              deviceId: item.id,
              deviceSn: item.deviceSn,
              // gatewayDeviceSn: item.deviceList[0]?.deviceSn,
              gatewayDeviceSn: item.deviceSn,
              cameraId: item.liveCapacity?.device_list[0]?.camera_list[0]?.camera_index,
              videoIndex: item.liveCapacity?.device_list[0]?.camera_list[0]?.video_list[0]?.video_index,
              cameraType: 0,
            },
            info: {
              deviceName: item.deviceName,
              liveStreaming: false,
            },
            LiveList: {},
          };

          // if (LiveAirport.value.length > 6 ) {
          //   return;
          // }

          LiveAirport.value.push(params);

          // startLive(params)
          //   .then((res) => {
          //     let obg = {
          //       videoUrl: res.urlType == 0 ? null : res.livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv'),
          //       appId: res.livePlayAgoraUrl.appid,
          //       channel: res.livePlayAgoraUrl.channel,
          //       token: res.livePlayAgoraUrl.token,
          //       urlType: res.urlType,
          //       deviceName: item.deviceName,
          //     };
          //     LiveAirport.value.push(obg);
          //   })
          //   .catch((err) => {
          //     LiveAirport.value.push({
          //       videoUrl: null,
          //       appId: null,
          //       channel: null,
          //       token: null,
          //       urlType: 1,
          //       deviceName: null,
          //     });
          //   });

          console.log(LiveAirport.value, 'LiveAirport');
        });
      });

    getTaskFlightStatistics();
    getMarkCategoryList();

    // if (DockShow.droneId) {
    //   await shadowGet(DockShow.droneId).then(ress => {
    //     // lat.value = coordtransform.wgs84togcj02(ress.aircraftShadow.longitude, ress.aircraftShadow.latitude)
    //     lat.value = [ress.aircraftShadow.longitude, ress.aircraftShadow.latitude]

    //   })
    // }

    if (DockShow.deviceId) {
      shadowGet(DockShow.deviceId).then((res) => {
        // console.log(res)
      });
    }

    // await list({ pageNo: 1, pageSize: 99999 }).then(res => {
    const arr = {
      projectIds: [localStorage.getItem('Project_Id')],
      sysMultiOrgCode: selectedDepartment.value.join(),
      deptQueryType: 'MULTIPLE',
    };
    // res.records.forEach(item => {
    //   arr.projectIds.push(item.id)
    // })
    // console.log(arr)
    mediaFile.queryDroneFlightStats(arr).then((res) => {
      flightStatistics.value = res; //飞行统计
    });
    // })

    mediaFile.queryAlarmStats(arr).then((res) => {
      //告警日志
      AlarmStats.value = res;
    });
  }

  const getTaskFlightStatistics = () => {
    mediaFile
      .taskFlightStatistics({ ...taskFlightParams, sysMultiOrgCode: selectedDepartment.value.join(','), deptQueryType: 'MULTIPLE' })
      .then((res) => {
        TaskFlightStatisticsList.value.push(res.records);
        console.log(res.records[0], 'res.records');
        console.log(TaskFlightStatisticsList.value, 'TaskFlightStatisticsList');
      });
  };

  const getTaskStatistics = (time: string = 'all') => {
    mediaFile
      .taskStatistics({
        taskIndexStatisticsType: time,
        sysMultiOrgCode: selectedDepartment.value.join(),
        deptQueryType: 'MULTIPLE',
      })
      .then((res) => {
        console.log(res, 'getTaskStatistics');
        taskStatisticsList.value = res;
        setTimeout(() => {
          pieChartChartData.value[0].value = res.completeTask;
          pieChartChartData.value[1].value = res.failedTask;
          pieChartChartData.value[2].value = res.waitExecuteTask;
          pieChartChartData.value[3].value = res.inExecuteTask;
          pieChartRef.value.initCharts();
        }, 100);
        console.log(pieChartRef.value, 'pieChartChartData');
      });
  };

  const getMarkCategoryList = async () => {
    try {
      const res = await mediaFile.markCategoryList({ sysMultiOrgCode: selectedDepartment.value.join(), deptQueryType: 'MULTIPLE' });

      // 每个子数组的最大长度
      const maxChunkSize = 10;
      // 需要保留的最大数组数量
      const maxChunks = 5;
      // 将 res 按照 maxChunkSize 分割成多个数组
      const chunks = [];
      for (let i = 0; i < res.length; i += maxChunkSize) {
        chunks.push(res.slice(i, i + maxChunkSize));
      }

      // 只保留前 maxChunks 个数组
      const result = chunks.slice(0, maxChunks).map((chunk) => ({
        arr: chunk.map((item) => ({
          name: item.name,
          // value: item.children?.length || 0,
          value: item.markCount || 0,
        })),
      }));

      markCategoryClassify.value = result;
      toggleLabelData(result[0], 0);
      console.log(markCategoryClassify.value, 'markCategoryClassify.value');
    } catch (error) {
      console.error('Failed to fetch mark category list:', error);
    }
  };

  const StartLiveClick = (item, index) => {
    console.log(item, index);
    startLive(item.params).then(({ urlType, livePalyUrl, livePlayAgoraUrl, deviceLiveId, wsUrl }) => {
      const { LiveList, info } = LiveAirport.value[index];
      // LiveList['videoUrl'] = urlType === 0 ? null : livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv');
      LiveList['videoUrl'] = urlType === 0 ? null : wsUrl;
      Object.assign(LiveList, {
        appId: livePlayAgoraUrl?.appid,
        channel: livePlayAgoraUrl?.channel,
        token: livePlayAgoraUrl?.token,
        urlType: urlType,
        deviceLiveId: deviceLiveId,
      });

      info.liveStreaming = true;
    });

    console.log(LiveAirport.value[index]);
  };

  const closeLiveClick = (item, index) => {
    stopLive({
      deviceLiveId: item.LiveList.deviceLiveId,
      deviceSn: item.params.deviceSn,
    }).then((res) => {
      LiveAirport.value[index].info.liveStreaming = false;
      LiveAirport.value[index].LiveList = {};
      console.log(LiveAirport.value[index], '直播结束');
    });
  };

  const VideoButton = (type: Boolean) => {
    if (LiveAirport.value.length == 0) return;
    if (type) {
      LiveAirport.value.forEach((item, index) => {
        if (!item.info.liveStreaming && Object.keys(item.LiveList).length == 0) {
          StartLiveClick(item, index);
        }
      });
    } else {
      LiveAirport.value.forEach((item, index) => {
        if (item.info.liveStreaming && Object.keys(item.LiveList).length > 0) {
          closeLiveClick(item, index);
        }
      });
    }
  };

  const info = (event) => {
    console.log(event);
    LiveStreamList.value = [];
    event.liveCapacity.device_list.forEach((item) => {
      item.camera_list.forEach((item2) => {
        let liveParams = {
          gatewayDeviceSn: event.deviceSn,
          deviceId: item.sn == event.deviceSn ? event.id : event.deviceList[0].id,
          deviceSn: event.deviceSn,
          cameraId: item2.camera_index,
          videoIndex: item2.video_list[0].video_index,
          cameraType: item.sn == event.deviceSn ? 0 : 1,
        };
        startLive(liveParams).then((res) => {
          const districtMatch = event.lastLocation.match(/[\u4e00-\u9fa5]+区/);
          let liveStream = {
            // videoUrl: res.urlType == 0 ? null : res.livePalyUrl.replace('http://', 'ws://').replace('/hls.m3u8', '.live.flv'),
            videoUrl: res.urlType == 0 ? null : res.wsUrl,
            appId: res.livePlayAgoraUrl?.appid,
            channel: res.livePlayAgoraUrl?.channel,
            token: res.livePlayAgoraUrl?.token,
            urlType: res.urlType,
            district: districtMatch ? districtMatch[0] : '未知区',
            lastLocation: event.lastLocation,
          };

          LiveStreamList.value.push(liveStream);
        });
      });
    });
    console.log(LiveStreamList.value, 'LiveStreamList------------++++++++++');
  };

  const marsOnload = (map) => {
    Map = map;
    console.log(lat.value, map, 'lat');
  };

  function mediaDataStatist() {
    mediaFile
      .queryMediaCountByProjects({
        projectIds: localStorage.getItem('Project_Id')?.split(',') || [],
        sysMultiOrgCode: selectedDepartment.value.join(),
        deptQueryType: 'MULTIPLE',
      })
      .then((res) => {
        // 检查res是否为数组且有元素，避免Index out of bounds错误
        if (Array.isArray(res) && res.length > 0) {
          totalData.value = res[0].total;
        } else {
          totalData.value = 0;
        }
      });
    chartData.value = [];

    mediaFile
      .queryMediaCountByTypes({
        projectIds: localStorage.getItem('Project_Id')?.split(',') || [],
        sysMultiOrgCode: selectedDepartment.value.join(),
        deptQueryType: 'MULTIPLE',
      })
      .then((ress) => {
        // 检查ress是否为数组，避免Index out of bounds错误
        if (Array.isArray(ress)) {
          ress.map((item) => {
            chartData.value.push({
              name: item.fileTypeName,
              value: item.total,
            });
          });
        }
      });

    mediaFile
      .deviceStats({
        sysMultiOrgCode: selectedDepartment.value.join(),
        deptQueryType: 'MULTIPLE',
      })
      .then((item) => {
        equipInform.value = item;
        // console.log(item, 'dddddddddddd')
      });
  }

  const toggleLabelData = (item, index) => {
    markCategoryIndex.value = index;
    console.log(item, index);
    markCategory.value = item.arr.slice(0, 6);
  };

  const toggleButton = (type) => {
    SelectedType.value = type;
    getTaskStatistics(type);
  };

  const getItemTitle = () => {
    queryById({ id: localStorage.getItem('Project_Id') }).then((res) => {
      homePageTitle.value = res.homePageTitle || title;
    });
  };

  var DockShow = {};

  const enterFullScreen = () => {
    // const element = document.documentElement; // 获取文档的根元素
    const element = document.getElementById('well-container'); // 通过 ID 获取指定的元素 homePageID
    // const element = document.getElementById('homePageID'); // 通过 ID 获取指定的元素

    if (element.requestFullscreen) {
      element.requestFullscreen(); // 使用原生的 requestFullscreen 方法进入全屏模式
    } else if (element.mozRequestFullScreen) {
      element.mozRequestFullScreen(); // Firefox
    } else if (element.webkitRequestFullscreen) {
      element.webkitRequestFullscreen(); // Chrome, Safari 和 Opera
    } else if (element.msRequestFullscreen) {
      element.msRequestFullscreen(); // IE/Edge
    }

    // Map.reload()
  };

  const exitFullScreen = () => {
    if (document.exitFullscreen) {
      document.exitFullscreen(); // 退出全屏模式
    } else if (document.mozCancelFullScreen) {
      document.mozCancelFullScreen(); // Firefox
    } else if (document.webkitExitFullscreen) {
      document.webkitExitFullscreen(); // Chrome, Safari 和 Opera
    } else if (document.msExitFullscreen) {
      document.msExitFullscreen(); // IE/Edge
    }
  };
  const handleResize = () => {
    // 窗口变化时的处理逻辑
    winHit.value = window.innerHeight;
    winWdt.value = window.innerWidth;
  };
  const selectChange = (value) => {
    if (value.length === 0) {
      // 禁止清空，恢复上一次值
      selectedDepartment.value = [...lastValidValue];
      return;
    } else {
      lastValidValue = [...value];
    }
    mediaDataStatist();
    getData();
    getTaskStatistics();
  };
  function normalizeTree(list = []) {
    return list.map((item) => ({
      ...item,
      key: item.orgCode,
      value: item.orgCode,
      disabled: item.disableCheckbox === true,
      children: item.children ? normalizeTree(item.children) : [],
    }));
  }
  let winHit = ref(919);
  let winWdt = ref(1920);
  onMounted(() => {
    const myDepart = JSON.parse(localStorage.getItem('myDepartList'));
    const myDepartAndChildrenTree = localStorage.getItem('myDepartAndChildrenTree');
    // departmentList.value = myDepart && myDepart != 'null' ? JSON.parse(myDepart) : [];
    // departmentList.value = myDepartAndChildrenTree && myDepartAndChildrenTree != 'null' ? JSON.parse(myDepartAndChildrenTree) : [];
    departmentList.value = myDepartAndChildrenTree && myDepartAndChildrenTree !== 'null' ? normalizeTree(JSON.parse(myDepartAndChildrenTree)) : [];
    if (myDepart.length > 0) {
      selectedDepartment.value = [myDepart[0].orgCode];
      lastValidValue = [...selectedDepartment.value];
    }
    console.log('当前环境：', localStorage.getItem('environmentTag'));
    // 组件挂载时开始监听窗口变化
    window.addEventListener('resize', handleResize);
    winHit.value = window.innerHeight;
    winWdt.value = window.innerWidth;
    getItemTitle();
    // if (localStorage.getItem('homePageTitle')) {
    //   homePageTitle.value = localStorage.getItem('homePageTitle') == 'undefined' || localStorage.getItem('homePageTitle') == 'null' ? title : localStorage.getItem('homePageTitle')
    // } else {
    //   homePageTitle.value = title
    // }

    getData();
    // startLiveBroadcast();
    // mapInit()
    mediaDataStatist();

    getTaskStatistics();
  });

  onUnmounted(() => {
    clearInterval(time);
    // 组件卸载时停止监听窗口变化
    window.removeEventListener('resize', handleResize);
  });
</script>
<style>
  /* ::-webkit-scrollbar {
    display: none;
  } */
</style>
<style scoped lang="less">
  img {
    -webkit-user-drag: none;
  }

  * {
    user-select: none;
  }

  /* 修改垂直滚动条样式 */
  /* 隐藏默认滚动条 */
  ::-webkit-scrollbar {
    display: none;
  }

  .homePageBox {
    // position: relative;
    width: 100%;
    height: 100%;
    // position: absolute;

    .mapBox {
      display: flex;
      justify-content: space-between;
      align-items: center;
      background-position: center;
      height: 100%;
      width: 100%;
      position: absolute;
      background-size: cover;
      /* 让背景图充满整个元素 */
      background-repeat: no-repeat;
      /* 禁止平铺 */

      .container {
        width: 100%;
        // height: 55rem;
        height: 100%;
        position: absolute;
        // top: 50%;
        // left: 50%;
        // transform: translate(-50%, -50%);
        // border-radius: 4px;
        // margin-top: 1.125rem;
        // overflow: hidden;
      }

      .homePageLeft {
        width: 26.5rem;
        // width: 22%;
        // position: absolute;
        // top: 7.1875rem;
        // left: 1.5rem;
        margin-left: 1.5rem;
        // margin-top: 1.125rem;
        z-index: 10;
        // backdrop-filter: blur(10px);
        background-color: rgba(0, 0, 0, 60%);
        overflow: scroll;
        // height: calc(100% - 1.125rem - 1.125rem);
        // height: 803px;
        height: 96%;
        display: flex;
        flex-direction: column;
        .equipInform {
          border-radius: 4px;
          position: relative;
          .treeSelectBox {
            position: absolute;
            right: 0;
            display: flex;
            align-items: center;
            height: 40px;
          }
          .head {
            background: linear-gradient(82deg, rgba(9, 40, 63, 0.5) 0%, rgba(5, 11, 39, 0) 100%);
            border-radius: 4px;
            display: flex;
            align-items: center;

            .icon {
              img {
              }
            }

            .text {
              font-size: 0.875rem;
              font-weight: 600;
              color: #ffffff;
              text-shadow: 0px 0px 12px #158eff;
            }
          }
          .numberDevices {
            width: 96%;
            // padding: 0 1%;
            margin-left: 2%;
            display: flex;
            justify-content: space-around;
            .left {
              width: 48.5%;
              .numberDevice-left {
                background: linear-gradient(135deg, rgba(142, 245, 255, 0.15) 0%, rgba(85, 231, 255, 0) 100%);
                box-shadow: inset 0px 0px 5px 0px rgba(80, 247, 255, 0.7);
                border-radius: 4px;
                // border: 2px solid;
                border-image: linear-gradient(142deg, rgba(130, 255, 243, 0.5), rgba(75, 255, 227, 0)) 2 2;
                display: flex;
                justify-content: space-between;
                align-items: center;
                padding: 0.5rem 0.8125rem 0.5rem 0.8125rem;
                // margin-top: 1.5625rem;

                img {
                }

                .quantity {
                  display: flex;
                  flex-direction: column;

                  .Titel {
                    font-size: 0.8rem;
                    font-weight: 400;
                    color: #ffffff;
                  }

                  .number {
                    font-size: 1rem;
                    font-weight: 600;
                    color: #ffffff;
                    text-align: center;
                  }
                }

                /deep/ .ant-progress-inner {
                  width: 44px !important;
                  height: 44px !important;
                }
              }
            }
            .right {
              width: 48.5%;
              .numberDeviceTwo-right {
                background: linear-gradient(135deg, rgba(142, 184, 255, 0.2) 0%, rgba(85, 144, 255, 0) 100%);
                box-shadow: inset 0px 0px 8px 0px rgba(80, 165, 255, 0.7);
                border-radius: 4px;
                // border: 2px solid;
                border-image: linear-gradient(142deg, rgba(130, 214, 255, 0.5), rgba(75, 159, 255, 0)) 2 2;
                display: flex;
                align-items: center;
                padding: 0.5rem 0.8125rem 0.5rem 0.8125rem;
                justify-content: space-between;
                // margin-top: 1rem;
                // gap: 4.5625rem;

                img {
                }

                .quantity {
                  display: flex;
                  flex-direction: column;

                  .Titel {
                    font-size: 0.8rem;
                    font-weight: 400;
                    color: #ffffff;
                  }

                  .number {
                    font-size: 1rem;
                    font-weight: 600;
                    color: #ffffff;
                    text-align: center;
                  }
                }

                /deep/ .ant-progress-inner {
                  width: 2.75rem !important;
                  height: 2.75rem !important;
                }
              }
            }
          }

          .numberDevice {
            background: linear-gradient(135deg, rgba(142, 245, 255, 0.15) 0%, rgba(85, 231, 255, 0) 100%);
            box-shadow: inset 0px 0px 5px 0px rgba(80, 247, 255, 0.5);
            border-radius: 4px;
            border: 2px solid;
            border-image: linear-gradient(142deg, rgba(130, 255, 243, 0.5), rgba(75, 255, 227, 0)) 2 2;
            display: flex;
            justify-content: space-between;
            padding: 1.25rem 0.8125rem 1.25rem 1.25rem;
            margin-top: 1.5625rem;

            img {
            }

            .quantity {
              display: flex;
              flex-direction: column;

              .Titel {
                font-size: 0.8725rem;
                font-weight: 400;
                color: #ffffff;
              }

              .number {
                font-size: 1.125rem;
                font-weight: 600;
                color: #ffffff;
                text-align: center;
              }
            }

            /deep/ .ant-progress-inner {
              width: 44px !important;
              height: 44px !important;
            }
          }

          .numberDeviceTwo {
            background: linear-gradient(135deg, rgba(142, 184, 255, 0.2) 0%, rgba(85, 144, 255, 0) 100%);
            box-shadow: inset 0px 0px 8px 0px rgba(80, 165, 255, 0.47);
            border-radius: 4px;
            border: 2px solid;
            border-image: linear-gradient(142deg, rgba(130, 214, 255, 0.5), rgba(75, 159, 255, 0)) 2 2;
            display: flex;
            padding: 1.25rem 13px 1.25rem 1.25rem;
            margin-top: 1rem;
            gap: 4.5625rem;

            img {
            }

            .quantity {
              display: flex;
              flex-direction: column;

              .Titel {
                font-size: 0.875rem;
                font-weight: 400;
                color: #ffffff;
              }

              .number {
                font-size: 1.125rem;
                font-weight: 600;
                color: #ffffff;
                text-align: center;
              }
            }

            /deep/ .ant-progress-inner {
              width: 2.75rem !important;
              height: 2.75rem !important;
            }
          }
        }

        .dataStatist {
          // margin-top: 1.8125rem;
          position: relative;
          .head {
            background: linear-gradient(82deg, rgba(9, 40, 63, 0.5) 0%, rgba(5, 11, 39, 0) 100%);
            border-radius: 4px;
            display: flex;
            align-items: center;

            .icon {
              img {
              }
            }

            .text {
              font-size: 0.875rem;
              font-weight: 600;
              color: #ffffff;
              text-shadow: 0px 0px 12px #158eff;
            }
          }

          .totalData {
            display: flex;
            align-items: center;
            // margin-top: 0.875rem;

            .total {
              font-size: 0.875rem;
              font-weight: 500;
              color: #ffffff;
              margin-right: 0.6875rem;
              margin-left: 0.6875rem;
            }

            .today {
              font-size: 0.75rem;
              font-weight: 400;
              color: #ffffff;
              margin-right: 0.4375rem;
            }
          }

          .dotBox {
            position: absolute;
            bottom: 10%;
            display: flex;
            justify-content: center;
            gap: 10px;
            width: 80%;
            left: 6%;

            .dot {
              width: 7px;
              height: 7px;
              background: rgba(255, 255, 255, 0.4);
              border-radius: 50%;
              cursor: pointer;
            }

            .dotBack {
              background: rgba(255, 255, 255, 0.8);
            }
          }
        }

        .alarm {
          // height: 32%;
          flex: 1;
          display: flex;
          flex-direction: column;
          .head {
            background: linear-gradient(82deg, rgba(9, 40, 63, 0.5) 0%, rgba(5, 11, 39, 0) 100%);
            border-radius: 4px;
            display: flex;
            align-items: center;

            .icon {
              img {
              }
            }

            .text {
              font-size: 0.875rem;
              font-weight: 600;
              color: #ffffff;
              text-shadow: 0px 0px 0.78rem #158eff;
            }
          }

          .alarmInformBox {
            margin-top: 0.4rem;
            padding-left: 0.7rem;
            // height: 11rem;
            // height: calc(100% - 900px);
            overflow: scroll;

            .alarmInform {
              background: linear-gradient(297deg, rgba(78, 122, 255, 0.11) 0%, rgba(95, 174, 255, 0.24) 100%);
              box-shadow: inset 0px 0px 3px 0px rgba(110, 187, 229, 0.2);
              border-radius: 4px;
              width: 12.1875rem;
              height: 5.0625rem;
              padding: 0.5625rem 0.2rem;
              display: flex;
              flex-direction: column;
              justify-content: space-between;

              .alarmInformTop {
                display: flex;
                align-items: center;
                justify-content: space-between;

                .Icon {
                }

                .titleBox {
                  .title {
                    font-size: 0.75rem;
                    font-weight: 500;
                    color: #ffffff;
                  }

                  .time {
                    font-size: 0.75rem;
                    font-weight: 400;
                    color: #ffffff;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                  }
                }

                .status {
                  font-size: 0.75rem;
                  font-weight: 500;
                  color: #fffa7f;
                  background: linear-gradient(102deg, #f3db3a 0%, rgba(243, 217, 39, 0.12) 100%);
                  border-image: linear-gradient(324deg, rgba(255, 229, 47, 0), rgba(255, 246, 129, 0.77)) 1 1;
                  width: 2.25rem;
                  height: 1.25rem;
                  text-align: center;
                }
              }

              .alarmInformBot {
                background: linear-gradient(297deg, rgba(78, 122, 255, 0.11) 0%, rgba(95, 174, 255, 0.09) 100%);
                box-shadow: inset 0px 0px 3px 0px rgba(110, 187, 229, 0.5);
                border-radius: 4px;
                padding-left: 9px;
                font-size: 0.75rem;
                font-weight: 400;
                color: #ffffff;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
              }
            }
          }
        }
      }

      .homePageRight {
        width: 26.5rem;
        // width: 22%;
        // position: absolute;
        // top: 115px;
        // right: 24px;
        z-index: 10;
        // backdrop-filter: blur(10px);
        background-color: rgba(0, 0, 0, 60%);
        // margin-top: 1.125rem;
        margin-right: 1.5rem;
        // height: 823px;
        height: 96%;
        overflow: scroll;
        // height: calc(100% - 1.125rem - 1.125rem);
        display: flex;
        flex-direction: column;
        .realTimVideo {
          .head {
            background: linear-gradient(82deg, rgba(9, 40, 63, 0.5) 0%, rgba(5, 11, 39, 0) 100%);
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: space-between;

            .headNoe {
              display: flex;
              align-items: center;

              .icon {
                img {
                }
              }

              .text {
                font-size: 0.75rem;
                font-weight: 600;
                color: #ffffff;
                text-shadow: 0px 0px 0.75rem #158eff;
              }
            }

            .LiveSwitchingBox {
              .Switch {
                width: 25px;
                height: 25px;
                background: rgba(255, 255, 255, 0.2);
                border-radius: 4px;
              }
            }

            .headTwo {
              display: flex;
              gap: 8px;
              .time {
                .ant-badge {
                  /deep/.ant-badge-status-text {
                    color: #ffffff;
                  }
                }
              }

              .headBot {
                box-shadow: inset 0px 0px 8px 0px rgba(110, 168, 255, 0.93);
                border-radius: 2px;
                border: 1px solid rgba(94, 158, 255, 0.57);
                color: #ffffff;
                display: flex;
                align-items: center;
                padding: 0 8px;
                cursor: pointer;
              }
            }
          }

          .realTimVideoBox {
            padding-left: 12px;
            // margin-top: 16px;

            .videoBox {
              position: relative;

              .videoName {
                display: flex;
                align-items: center;
                position: absolute;
                top: 10px;
                left: 5px;

                .text {
                  color: #ffffff;
                  font-size: 12px;
                  font-weight: 400;
                  margin-left: 5px;
                }
              }

              .video {
                width: 126px;
                height: 105px;
                background-color: #020e1c;
              }

              .textBox {
                height: 25px;
                width: 126px;
                background-color: #040f16;

                display: flex;
                align-items: center;
                padding-left: 8px;

                .ant-badge {
                  /deep/.ant-badge-status-text {
                    color: #37fcb6;
                  }
                }
              }
            }

            // padding-left: 0.75rem;
            // margin-top: 1rem;

            // .videoBox {
            //   position: relative;

            //   .videoName {
            //     display: flex;
            //     align-items: center;
            //     position: absolute;
            //     top: 10px;
            //     left: 5px;

            //     .text {
            //       color: #FFFFFF;
            //       font-size: 12px;
            //       font-weight: 400;
            //       margin-left: 5px;
            //     }
            //   }

            //   .video {
            //     width: 126px;
            //     height: 105px;
            //     background-color: #020e1c;
            //   }

            //   .textBox {
            //     height: 25px;
            //     width: 126px;
            //     background-color: #040f16;

            //     display: flex;
            //     align-items: center;
            //     padding-left: 8px;

            //     .ant-badge {
            //       /deep/.ant-badge-status-text {
            //         color: #37fcb6;
            //       }
            //     }
            //   }
            // }

            // .videoBox {
            //   height: 14.1875rem;
            //   overflow: scroll;

            .screenNoe {
              width: 25rem;
              // width: 97%;
              height: 14.1875rem;
              box-shadow: inset 0px 0px 13px 0px rgba(94, 208, 255, 0.15);
              border: 1px solid;
              border-image: linear-gradient(180deg, rgba(171, 213, 255, 0.72), rgba(114, 209, 255, 0.34)) 1 1;

              .NoeBox {
                // width: 100%;
                width: 25rem;
                height: 12.0625rem;
                position: relative;
                padding: 1px;
                overflow: hidden;

                .WaitPlay {
                  width: 99.7%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: rgba(0, 0, 0, 0.24);
                  position: absolute;
                  z-index: 100;
                }
                .WaitPlayHover {
                  width: 99.7%;
                  height: 100%;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  background-color: rgba(0, 0, 0, 0.24);
                  position: absolute;
                  z-index: 100;
                  opacity: 0;
                }
                .WaitPlayHover:hover {
                  opacity: 1;
                }
              }

              .NoeTitle {
                height: 1.6875rem;
                display: flex;
                align-items: center;
                padding-left: 0.75rem;
              }
            }

            .screenTwo {
              width: 25rem;
              height: 14.1875rem;
              display: flex;
              justify-content: space-between;

              .TwoBox {
                width: 12.125rem;
                height: 14.1875rem;
                box-shadow: inset 0px 0px 13px 0px rgba(94, 208, 255, 0.15);
                border: 1px solid;
                border-image: linear-gradient(180deg, rgba(171, 213, 255, 0.72), rgba(114, 209, 255, 0.34)) 1 1;

                .TwoVideo {
                  width: 100%;
                  height: 12.0625rem;
                  position: relative;
                  padding: 1px;
                  overflow: hidden;
                  .WaitPlay {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                  }
                  .WaitPlayHover {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                    opacity: 0;
                  }
                  .WaitPlayHover:hover {
                    opacity: 1;
                  }
                }

                .TwoTitle {
                  height: 1.6875rem;
                  display: flex;
                  align-items: center;
                  padding-left: 0.75rem;
                }
              }
            }

            .screenThree {
              width: 25rem;
              height: 14.1875rem;
              display: flex;
              justify-content: space-between;

              .ThreeLeft {
                width: 12.125rem;
                height: 14.1875rem;
                box-shadow: inset 0px 0px 13px 0px rgba(94, 208, 255, 0.15);
                position: relative;
                border: 1px solid;
                border-image: linear-gradient(180deg, rgba(171, 213, 255, 0.72), rgba(114, 209, 255, 0.34)) 1 1;

                .LeftVideo {
                  width: 99.7%;
                  height: 12.0625rem;
                  position: relative;
                  padding: 1px;
                  overflow: hidden;
                  .WaitPlay {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                  }
                  .WaitPlayHover {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                    opacity: 0;
                  }
                  .WaitPlayHover:hover {
                    opacity: 1;
                  }
                }

                .LeftTitle {
                  height: 1.6875rem;
                  display: flex;
                  align-items: center;
                  padding-left: 0.75rem;
                }
              }

              .Threeright {
                width: 12.125rem;
                height: 14.1875rem;
                display: flex;
                flex-direction: column;
                justify-content: space-between;

                .rightTop {
                  width: 12.125rem;
                  height: 6.6875rem;
                  box-shadow: inset 0px 0px 13px 0px rgba(94, 208, 255, 0.15);
                  position: relative;
                  border: 1px solid;
                  border-image: linear-gradient(180deg, rgba(171, 213, 255, 0.72), rgba(114, 209, 255, 0.34)) 1 1;

                  .TopVideo {
                    width: 99.7%;
                    height: 5rem;
                    position: relative;
                    padding: 1px;
                    overflow: hidden;
                    .WaitPlay {
                      width: 99.7%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      background-color: rgba(0, 0, 0, 0.24);
                      position: absolute;
                      z-index: 100;
                    }
                    .WaitPlayHover {
                      width: 99.7%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      background-color: rgba(0, 0, 0, 0.24);
                      position: absolute;
                      z-index: 100;
                      opacity: 0;
                    }
                    .WaitPlayHover:hover {
                      opacity: 1;
                    }
                  }

                  .TopTitle {
                    height: 1.6875rem;
                    display: flex;
                    align-items: center;
                    padding-left: 0.75rem;
                  }
                }

                .rightBot {
                  width: 12.125rem;
                  height: 6.6875rem;
                  position: relative;
                  box-shadow: inset 0px 0px 13px 0px rgba(94, 208, 255, 0.15);
                  border: 1px solid;
                  border-image: linear-gradient(180deg, rgba(171, 213, 255, 0.72), rgba(114, 209, 255, 0.34)) 1 1;

                  .BotVideo {
                    width: 99.7%;
                    height: 5rem;
                    position: relative;
                    padding: 1px;
                    overflow: hidden;
                    .WaitPlay {
                      width: 99.7%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      background-color: rgba(0, 0, 0, 0.24);
                      position: absolute;
                      z-index: 100;
                    }
                    .WaitPlayHover {
                      width: 99.7%;
                      height: 100%;
                      display: flex;
                      align-items: center;
                      justify-content: center;
                      background-color: rgba(0, 0, 0, 0.24);
                      position: absolute;
                      z-index: 100;
                      opacity: 0;
                    }
                    .WaitPlayHover:hover {
                      opacity: 1;
                    }
                  }

                  .BotTitle {
                    height: 1.6875rem;
                    display: flex;
                    align-items: center;
                    padding-left: 0.75rem;
                  }
                }
              }
            }

            .screenMore {
              width: 12.125rem;
              height: 6.6875rem;
              box-shadow: inset 0px 0px 13px 0px rgba(94, 208, 255, 0.15);
              border: 1px solid;
              border-image: linear-gradient(180deg, rgba(171, 213, 255, 0.72), rgba(114, 209, 255, 0.34)) 1 1;

              .MorsTop {
                width: 100%;
                height: 5rem;
                display: flex;
                justify-content: center;
                align-items: flex-end;
                background-color: #02080e;

                .videoBox {
                  width: 100%;
                  height: 4.75rem;
                  position: relative;
                  border-radius: 4px;
                  overflow: hidden;
                  .WaitPlay {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                  }
                  .WaitPlayHover {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                    opacity: 0;
                  }
                  .WaitPlayHover:hover {
                    opacity: 1;
                  }
                }
              }

              .MorsBot {
                height: 1.6875rem;
                display: flex;
                align-items: center;
                padding-left: 0.75rem;
              }
            }

            .screenMulti {
              width: 7.875rem;
              height: 6.5625rem;
              box-shadow: inset 0px 0px 13px 0px rgba(94, 208, 255, 0.15);
              border: 1px solid;
              border-image: linear-gradient(180deg, rgba(171, 213, 255, 0.72), rgba(114, 209, 255, 0.34)) 1 1;

              .MorsTop {
                width: 100%;
                height: 5rem;
                display: flex;
                justify-content: center;
                align-items: flex-end;
                background-color: #02080e;

                .videoBox {
                  width: 8.0625rem;
                  height: 4.75rem;
                  position: relative;
                  border-radius: 4px;
                  overflow: hidden;
                  .WaitPlay {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                  }
                  .WaitPlayHover {
                    width: 99.7%;
                    height: 100%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    background-color: rgba(0, 0, 0, 0.24);
                    position: absolute;
                    z-index: 100;
                    opacity: 0;
                  }
                  .WaitPlayHover:hover {
                    opacity: 1;
                  }
                }
              }

              .MorsBot {
                height: 1.6875rem;
                display: flex;
                align-items: center;
                padding-left: 0.75rem;
              }
            }
          }
        }

        .taskStatistBox {
          margin-top: 1rem;

          .head {
            background: linear-gradient(82deg, rgba(9, 40, 63, 0.5) 0%, rgba(5, 11, 39, 0) 100%);
            border-radius: 4px;
            display: flex;
            align-items: center;

            .icon {
              img {
              }
            }

            .text {
              font-size: 0.875rem;
              font-weight: 600;
              color: #ffffff;
              text-shadow: 0px 0px 0.75rem #158eff;
            }
          }

          .statistInform {
            display: flex;
            justify-content: center;
            // margin-top: 1.625rem;

            .chartBox {
              position: relative;

              .task {
                position: absolute;
                top: 3.5625rem;
                left: 3.5625rem;
                width: 5.4375rem;
                height: 5.4375rem;
                border-radius: 100%;
                border: 3px solid #1d2f4d;
                display: flex;
                flex-direction: column;
                justify-content: center;

                .number {
                  letter-spacing: 1px;
                  text-shadow: 0px 0px 0.375rem #66e1df;
                  font-weight: 600;
                  font-size: 1.25rem;
                  text-align: center;
                  color: #ffffff;
                }

                .text {
                  font-size: 0.75rem;
                  font-family:
                    PingFangSC,
                    PingFang SC;
                  font-weight: 400;
                  color: #d0deee;
                  text-align: center;
                }
              }
            }

            .Inform {
              .rise {
                width: 12.5rem;
                height: 2.0625rem;
                background: #083a60;
                border-radius: 4px;
                display: flex;
                align-items: center;
                padding-left: 0.75rem;

                .title {
                  font-size: 0.875rem;
                  font-weight: 400;
                  color: #d0deee;
                  margin-right: 0.75rem;
                }

                .text {
                  font-size: 0.875rem;
                  font-weight: 400;
                  color: #ffffff;
                  margin-right: 0.5rem;
                }
              }

              .completeBox {
                .complete {
                  display: flex;
                  justify-content: space-between;
                  padding: 0.8125rem 0px 0.5625rem 0px;
                  border-bottom: 1px solid #3d3d3d;

                  .boxOne {
                    display: flex;
                    align-items: center;

                    .block {
                      width: 0.75rem;
                      height: 0.75rem;
                      background: linear-gradient(180deg, #52fadd 0%, #35927c 100%);
                      border-radius: 1px;
                      margin-right: 0.5rem;
                    }

                    .zxz {
                      width: 0.75rem;
                      height: 0.75rem;
                      background: linear-gradient(180deg, #65a0fc 0%, #224c6a 100%);
                      border-radius: 1px;
                      margin-right: 0.5rem;
                    }
                    .red {
                      width: 0.75rem;
                      height: 0.75rem;
                      background: linear-gradient(180deg, #fc6566 0%, #6a2231 100%);
                      border-radius: 1px;
                      margin-right: 0.5rem;
                    }

                    .black {
                      width: 0.75rem;
                      height: 0.75rem;
                      background: #1d2f4d;
                      border-radius: 1px;
                      margin-right: 0.5rem;
                    }

                    .text {
                      font-size: 0.875rem;
                      font-weight: 400;
                      color: #ffffff;
                    }
                  }

                  .bixTwo {
                    display: flex;
                    align-items: center;

                    .number {
                      font-size: 0.875rem;
                      font-weight: 500;
                      color: #ffffff;
                      margin-right: 9px;
                      width: 22px;
                      margin-left: 10px;
                    }

                    .percentage {
                      font-size: 0.75rem;
                      font-weight: 400;
                      color: #d0deee;
                      width: 49px;
                    }
                  }
                }
              }
            }
          }
        }

        .flightStatist {
          // height: 30%;
          flex: 1;
          display: flex;
          flex-direction: column;
          .head {
            background: linear-gradient(82deg, rgba(9, 40, 63, 0.5) 0%, rgba(5, 11, 39, 0) 100%);
            border-radius: 4px;
            display: flex;
            align-items: center;

            .icon {
              img {
              }
            }

            .text {
              font-size: 0.75rem;
              font-weight: 600;
              color: #ffffff;
              text-shadow: 0px 0px 12px #158eff;
            }
          }

          .flightTimeBox {
            display: flex;
            justify-content: space-around;

            .flightTime {
              display: flex;
              align-items: center;

              .flightBox {
                .time {
                  font-size: 1rem;
                  font-weight: 600;
                  color: #ffffff;
                }

                .text {
                  font-size: 0.75rem;
                  color: #ffffff;
                }
              }
            }
          }

          .carouselListBox {
            // height: 60%;
            flex: 1;
            display: flex;
            flex-direction: column;
            .meterHead {
              display: flex;
              background-image: linear-gradient(-87deg, rgba(78, 122, 255, 0.11) 0%, rgba(95, 174, 255, 0.09) 93%);
              box-shadow: inset 0px 0px 3px 0px rgba(110, 187, 229, 0.5);
              padding: 0.46875rem 0 0.46875rem 1.0625rem;

              .title {
                font-size: 0.75rem;
                color: #ffffff;
                letter-spacing: 0.16375rem;
                font-weight: 400;
              }

              .title:nth-child(1) {
                width: 7rem;
              }

              .title:nth-child(2) {
                width: 4.875rem;
              }

              .title:nth-child(3) {
                width: 6.25rem;
              }

              .title:nth-child(4) {
                width: 6.25rem;
              }
            }

            .scrollDataBox {
              // height: 121px;
              // height: 82%;
              overflow: scroll;
              flex: 1;
              .scrollData {
                display: flex;
                justify-content: center;

                height: 1.85rem;
                align-items: center;

                .title {
                  font-size: 0.75rem;
                  color: #ffffff;
                  letter-spacing: 0.14125rem;
                  font-weight: 400;
                  white-space: nowrap;
                  /* 禁止换行 */
                  overflow: hidden;
                  /* 隐藏超出部分 */
                  text-overflow: ellipsis;
                  /* 显示省略号 */
                }

                .title:nth-child(1) {
                  width: 7rem;
                }

                .title:nth-child(2) {
                  width: 4.875rem;
                  color: #38fcb6;
                }

                .title:nth-child(3) {
                  width: 6.25rem;
                }

                .title:nth-child(4) {
                  width: 6.25rem;
                }
              }

              .scrollData:nth-child(odd) {
                // background: #07192a;
              }

              .scrollData:nth-child(even) {
                // background: #0c2133;
              }
            }
          }
        }
      }

      .BottomQuantity {
        width: 50%;
        height: 9%;
        position: absolute;
        left: 50%;
        bottom: 5.1rem;
        // top: 51rem;
        transform: translate(-50%, -50%);
        display: flex;
        flex-direction: column;
        align-items: center;

        .DateSwitching {
          width: 15.5rem;
          height: 2rem;
          background: rgba(11, 24, 41, 0.8);
          border-radius: 1rem;
          display: flex;
          justify-content: center;
          align-items: center;

          .toggleButton {
            width: 3.625rem;
            height: 1.875rem;
            font-weight: 500;
            font-size: 14px;
            color: #ffffff;
            border-radius: 1rem;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
          }

          .toggleButtonOptfor {
            background: linear-gradient(322deg, rgba(67, 131, 255, 0.8) 0%, rgba(75, 146, 255, 0.4) 100%);
            box-shadow: inset 0px 0px 8px 0px rgba(110, 168, 255, 0.93);
            border: 2px solid rgba(94, 158, 255, 0.57);
          }
        }

        .QuantityBox {
          margin-top: 1.25rem;
          display: flex;
          gap: 5.5rem;

          .statisticsBox {
            .top {
              width: 3.75rem;
              height: 3.75rem;

              border-radius: 80%;
              display: flex;
              flex-direction: column;
              align-items: center;
              justify-content: center;

              .uavImg {
              }

              .number {
                font-weight: 500;
                font-size: 14px;
                color: #ffffff;
                text-align: center;
              }
            }

            .text {
              font-weight: 600;
              font-size: 12px;
              color: #ffffff;
              text-align: center;
            }
          }

          .statisticsBox:nth-child(1) {
            .top {
              background: linear-gradient(329deg, rgba(97, 201, 255, 0.71) 0%, rgba(46, 150, 255, 0.42) 100%);
              box-shadow: inset 0px 0px 16px 0px rgba(203, 237, 255, 0.5);
              border: 2px solid #6acdff;
            }
          }

          .statisticsBox:nth-child(2) {
            .top {
              background: linear-gradient(329deg, rgba(255, 97, 145, 0.71) 0%, rgba(255, 46, 140, 0.42) 100%);
              box-shadow: inset 0px 0px 16px 0px rgba(255, 203, 203, 0.5);
              border: 2px solid #ff6a79;
            }
          }

          .statisticsBox:nth-child(3) {
            .top {
              background: linear-gradient(329deg, rgba(97, 122, 255, 0.71) 0%, rgba(46, 124, 255, 0.42) 100%);
              box-shadow: inset 0px 0px 16px 0px rgba(203, 237, 255, 0.5);
              border: 2px solid #6aa4ff;
            }
          }

          .statisticsBox:nth-child(4) {
            .top {
              background: linear-gradient(329deg, rgba(255, 136, 97, 0.71) 0%, rgba(255, 91, 46, 0.42) 100%);
              box-shadow: inset 0px 0px 16px 0px rgba(255, 217, 203, 0.5);
              border: 2px solid #ff776a;
            }
          }
        }
      }
    }

    .headBox {
      width: 100%;
      height: 4.8125rem;
      background-size: cover;
      /* 让背景图充满整个元素 */
      background-repeat: no-repeat;
      /* 禁止平铺 */
      position: relative;

      .numberProjectBox {
        display: flex;
        align-items: center;
        position: absolute;
        transform: translate(20%, -50%);
        top: 50%;
        left: 0%;

        .Icon {
          img {
          }
        }

        .text {
          font-size: 0.875rem;
          font-weight: 500;
          color: #bcdfff;
          letter-spacing: 3px;
        }
      }

      .title {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 2.125rem;
        font-weight: 600;
        color: #ffffff;
        letter-spacing: 7px;
        text-shadow: 0px 0px 8px #1283f6;
        -webkit-background-clip: text;
      }

      .headTime {
        position: absolute;
        transform: translate(-15%, -50%);
        top: 50%;
        right: 0%;
        font-size: 0.874rem;
        font-weight: 400;
        color: #ffffff;
        letter-spacing: 3px;
      }
    }
  }
  .AlarmStatsCenter {
    justify-content: center;
  }
</style>
