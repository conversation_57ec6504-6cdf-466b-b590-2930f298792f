import { defHttp } from '/@/utils/http/axios';

enum Api {
  getAreaList = '/uav/label/getAreaList', //获取地区
  getList = '/uav/report/list', //获取列表
  getQueryLabel = '/uav/label/queryLabel', //查询问题列表
  save = '/uav/report/save', //保存报告
  saveDraft = '/uav/report/saveDraft', //保存草稿
  get = '/uav/report/get', // 获取报告详情
  upload = '/uav/report/upload/', // 上传报告
  del = '/uav/report/del', //删除报告
  coverUpload = '/file/manage/upload', // 封面上传
  saveLogo = '/sys/project/setReportLogo', //保存logo到项目
  queryStaticImg = '/uav/orthoPhoto/staticImage', // 获取静态图
}

export const reportManageApi = {
  getAreaList: (params) => defHttp.post({ url: Api.getAreaList, params }),
  getQueryLabel: (params) => defHttp.post({ url: Api.getQueryLabel, params }),
  getList: (params) => defHttp.post({ url: Api.getList, params }),
  save: (params) => defHttp.post({ url: Api.save, params }),
  saveDraft: (params) => defHttp.post({ url: Api.saveDraft, params }),
  get: (params) => defHttp.get({ url: Api.get, params }),
  upload: (fileId, reportType, sysOrgCode, params) =>
    defHttp.post({
      url: Api.upload + fileId + '/' + reportType,
      params,
      headers: { 'Content-Type': 'multipart/form-data', 'X-Sys-Org-Code': sysOrgCode },
      timeout: 10 * 60 * 1000,
    }),
  del: (params) => defHttp.post({ url: Api.del + '/' + params.id }),
  coverUpload: (params) => defHttp.post({ url: Api.coverUpload, params, headers: { 'Content-Type': 'multipart/form-data' } }),
  saveLogo: (params) => defHttp.post({ url: Api.saveLogo, params }),
  queryStaticImg: (params) => defHttp.post({ url: Api.queryStaticImg, params }),
};
