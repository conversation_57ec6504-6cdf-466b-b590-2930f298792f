<template>
  <div id="tag-manage-wrapper">
    <div class="btn-box">
      <div class="expand-collapse"> 报告管理 </div>
      <div>
        <a-tree-select
          v-if="treeData.length"
          v-model:value="treeValue"
          show-search
          style="margin-right: 15px; min-width: 200px"
          :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
          placeholder="请选择部门"
          tree-checkable
          treeCheckStrictly
          tree-default-expand-all
          :tree-data="treeData"
          :fieldNames="{ label: 'departName', value: 'orgCode' }"
          @change="onTreeChange"
        >
        </a-tree-select>
        <a-select class="report-expand-select" ref="select" v-model:value="selectState" @change="stateChange">
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="0">草稿</a-select-option>
          <a-select-option value="1">已完成</a-select-option>
          <a-select-option value="2">生成中</a-select-option>
          <a-select-option value="3">生成失败</a-select-option>
        </a-select>
        <a-button class="set-cover" type="primary" @click="setCover">设置logo</a-button>
        <a-button type="primary" @click="addTag">创建报告</a-button>
      </div>
    </div>

    <a-table :columns="columns" :dataSource="data" rowKey="id" :indentSize="10" :pagination="pagination" @change="paginationChange">
      <template #bodyCell="{ record, column }">
        <template v-if="column.dataIndex === 'state'">
          <span>
            <a-tag :color="record.state == '2' ? 'blue' : record.state == '1' ? 'green' : record.state == '3' ? 'red' : ''">
              {{ record.stateValue }}
            </a-tag>
          </span>
        </template>
        <template v-if="column.dataIndex === 'operation'">
          <a-button v-if="record.state == 0 || record.state == 1" type="link" @click="previewRow(record)" style="margin-right: 15px">预览</a-button>
          <a-button
            v-if="isMyChargePart(record.sysOrgCode) && (record.state == 0 || record.state == 3)"
            type="link"
            @click="editRow(record)"
            style="margin-right: 15px"
            >编辑</a-button
          >
          <a-button v-if="record.state == 1" type="link" @click="downloadRow(record)" style="margin-right: 15px">下载</a-button>
          <a-popconfirm
            v-if="isMyChargePart(record.sysOrgCode) && (record.state == 0 || record.state == 1 || record.state == 3)"
            title="确定删除该报告吗？"
            ok-text="确定"
            cancel-text="取消"
            @confirm="deleteRow(record)"
          >
            <a-button type="link">删除</a-button>
          </a-popconfirm>
        </template>
      </template>
    </a-table>
    <a-modal
      :title="modalType == 1 ? '编辑报告' : '创建报告'"
      width="1100px"
      :footer="null"
      v-model:visible="isVisible"
      :getContainer="getContainer"
      :destroyOnClose="true"
      :maskClosable="false"
      @cancel="cancelModal"
    >
      <addModal v-if="!isShowReportTemplate" :editRowId="editRowId" @handleCancel="handleModal" @nextModal="openReportTemplate"></addModal>
      <reportTemplate v-else :editRowId="editRowId" :sysOrgCode="sysOrgCode" @handleCancel="handleModal"></reportTemplate>
    </a-modal>

    <a-modal title="预览" width="800px" :footer="null" v-model:visible="isPreviewVisible" :getContainer="getContainer" :destroyOnClose="true">
      <week-report v-if="selectRow.reportType == '0'" ref="weekComponent" :dataInfo="selectRow" />
      <month-report v-else ref="monthComponent" :dataInfo="selectRow" />
    </a-modal>

    <a-modal
      class="cover-modal"
      title="设置logo"
      width="400px"
      v-model:visible="isCoverVisible"
      :getContainer="getContainer"
      :destroyOnClose="true"
      @ok="saveLogo"
      @cancel="cancelLogoModal"
    >
      <a-select
        class="department-select"
        placeholder="请选择部门"
        v-model:value="departmentKey"
        :options="departmentOptions"
        :fieldNames="{ label: 'departName', value: 'orgCode' }"
      >
      </a-select>
      <a-upload
        v-model:file-list="fileList"
        name="avatar"
        list-type="picture-card"
        class="avatar-uploader"
        :customRequest="customRequest"
        :show-upload-list="false"
        :before-upload="beforeUpload"
      >
        <img v-if="coverUrl" style="width: 200px" :src="coverUrl" />
        <div v-else>
          <plus-outlined :style="{ fontSize: '25px' }"></plus-outlined>
          <div class="ant-upload-text">上传</div>
        </div>
      </a-upload>
    </a-modal>
  </div>
</template>

<script lang="ts" name="dataManage-reportManage" setup>
import { ref, computed, onMounted, watch } from 'vue';
import { message } from 'ant-design-vue';
import { reportManageApi } from './reportManage.api';
import { useRouter } from 'vue-router';
import { InfoCircleOutlined, PlusOutlined, DeleteOutlined } from '@ant-design/icons-vue';
import { Tooltip } from 'ant-design-vue';
import reportTemplate from './reportTemplate.vue';
import html2pdf from 'html2pdf.js';
import html2canvas from 'html2canvas';
import addModal from './components/addModal.vue';
import weekReport from './components/weekReport.vue';
import monthReport from './components/monthReport.vue';
import { getDepartNameByCode } from '/@/utils/common/compUtils';

const router = useRouter();
const departmentKey = ref(localStorage.getItem('departmentKey'));
const departmentOptions = ref([]);
const columns = [
  {
    title: '报告名称',
    dataIndex: 'reportName',
    key: 'reportName',
  },
  {
    title: '所属部门',
    dataIndex: 'myDepartName',
    key: 'myDepartName',
    align: 'center',
    ellipsis: true,
    width: 200,
    customRender: function ({ text, record }) {
      const { sysOrgCode } = record as any;
      return getDepartNameByCode(sysOrgCode);
    },
  },
  {
    title: '创建人',
    dataIndex: 'createBy',
    key: 'createBy',
  },
  {
    title: '状态',
    dataIndex: 'state',
    key: 'state',
  },
  {
    title: '创建时间',
    dataIndex: 'createTime',
    key: 'createTime',
    sorter: (a, b) => new Date(a.createTime) - new Date(b.createTime),
  },
  {
    title: '操作',
    dataIndex: 'operation',
    key: 'operation',
  },
];
const treeValue = ref<{ value: string; label: string }[]>([]);

const treeData = ref([]);

const data = ref([]);
const isVisible = ref(false);
const isPreviewVisible = ref(false);
const isCoverVisible = ref(false);
const modalType = ref(0);
const editRowId = ref('');
const fileList = ref([]);
const coverUrl = ref(localStorage.getItem('reportLogo'));

const parentOptions = computed(() => {
  return data.value.map((item) => ({ label: item.name, value: item.id }));
});

const getContainer = () => {
  return document.getElementById('tag-manage-wrapper');
};
//创建报告
const addTag = () => {
  // 显示标签输入框
  isVisible.value = true;
  // 隐藏报告模板
  isShowReportTemplate.value = false;
  // 清除编辑行的ID
  editRowId.value = '';
  // 设置弹框类型为添加
  modalType.value = 0;
};
const setCover = () => {
  isCoverVisible.value = true;
  const departList = localStorage.getItem('myResponsibleDepartList');
  departmentOptions.value = departList ? JSON.parse(departList) : [];
};
// modal弹窗默认的自身关闭按钮
const cancelModal = () => {
  console.log(999999);
  console.log(modalType.value);
  console.log(isShowReportTemplate.value);
  console.log(editRowId.value);
  if (modalType.value === 0 && isShowReportTemplate.value) {
    reportManageApi.del({ id: editRowId.value }).then((res) => {
      getList();
    });
  }
};
// 隐藏弹框
const handleModal = () => {
  isVisible.value = false;
  getList();
};

// 列表内选择按钮
const selectRow = ref({});
//删除
const deleteRow = (record) => {
  let params = {
    id: record.id,
  };
  reportManageApi.del(params).then((res) => {
    message.success('删除成功');
    getList();
  });
};
//预览
const previewRow = (record) => {
  let params = {
    id: record.id,
  };
  reportManageApi.get(params).then((res) => {
    res.reportContent.inspectionContents.sort((a, b) => a.parentLabelId - b.parentLabelId);
    selectRow.value = res;
    isPreviewVisible.value = true;
  });
};
//修改
const editRow = (record) => {
  // 将编辑行的id赋值给editRowId
  editRowId.value = record.id;
  // 设置模态框类型为1，表示编辑操作
  modalType.value = 1;
  // 隐藏报告模板
  isShowReportTemplate.value = false;
  // 显示弹框
  isVisible.value = true;
};
//下载
const downloadRow = (record) => {
  window.open(record.reportFileUrl);
};
const selectState = ref('');

// 列表搜索条件变更
const stateChange = () => {
  getList();
};
const onTreeChange = (newVal, label, extra) => {
  console.log('extra', extra);
  // 上一次操作的值，只有一个且是取消选中时
  if (extra.preValue.length === 1 && extra.checked === false) {
    treeValue.value = extra.preValue.slice();
    return;
  }
  // 正常逻辑
  getList();
};
const isShowReportTemplate = ref(false);
const sysOrgCode = ref(''); // 添加这一行来存储sysOrgCode的值

// add弹窗内下一步
const openReportTemplate = (id, sysOrgCodeValue) => {
  editRowId.value = id;
  sysOrgCode.value = sysOrgCodeValue; // 设置sysOrgCode值
  isShowReportTemplate.value = true;
};
// 列表翻页
const paginationChange = (pagination) => {
  getList();
};

const pagination = ref({
  total: 0,
  current: 1,
  pageSize: 10,
  change: paginationChange,
});
// 获取数据列表
const getList = () => {
  const params = {
    pageNo: pagination.value.current,
    pageSize: pagination.value.pageSize,
    state: selectState.value,
    deptQueryType: 'MULTIPLE',
    sysMultiOrgCode: treeValue.value.map((item) => item.value).join(','),
  };
  reportManageApi.getList(params).then((res) => {
    console.log('res=>>', res.records);
    pagination.value.total = res.total;
    pagination.value.current = res.current;
    pagination.value.pageSize = res.size;
    data.value = res.records;
    data.value.forEach((e) => {
      e.stateValue = e.state == 1 ? '已完成' : e.state == 2 ? '生成中' : e.state == 3 ? '生成失败' : '草稿';
    });
  });
};
const customRequest = (e) => {
  const { file } = e;
  let formData = new FormData();
  formData.append('file', file);
  reportManageApi.coverUpload(formData).then((res) => {
    coverUrl.value = res.url;
    localStorage.setItem('reportLogo', res.url);
  });
};
const beforeUpload = (file) => {
  const isJpgOrPng = file.type === 'image/jpeg' || file.type === 'image/png';
  if (!isJpgOrPng) {
    message.error('图片格式错误');
  }
  const isLt2M = file.size / 1024 / 1024 < 50;
  if (!isLt2M) {
    message.error('图片大小不能超过50MB!');
  }
  return isJpgOrPng && isLt2M;
};
const saveLogo = () => {
  let params = {
    id: localStorage.getItem('Project_Id'),
    reportLogo: coverUrl.value,
    sysOrgCode: departmentKey.value,
  };
  reportManageApi.saveLogo(params).then((res) => {
    cancelLogoModal();
  });
};
const cancelLogoModal = () => {
  coverUrl.value = localStorage.getItem('reportLogo');
  isCoverVisible.value = false;
};
const transformTreeData = (data) => {
  return data.map((node) => {
    const newNode = {
      ...node,
      disabled: node.disableCheckbox,
      label: node.departName,
      value: node.orgCode,
      key: node.orgCode,
    };
    delete newNode.disableCheckbox;

    if (node.children) {
      newNode.children = transformTreeData(node.children);
    }

    return newNode;
  });
};
const setDepartmentTree = async () => {
  // 获取部门树
  const treeObj = localStorage.getItem('myDepartAndChildrenTree');
  const rawData = treeObj ? JSON.parse(treeObj) : [];
  treeData.value = transformTreeData(rawData);

  // 获取所属部门
  const myDepartList = localStorage.getItem('myDepartList');

  console.log('myDepartList', myDepartList);
  treeValue.value = myDepartList ? [{ value: JSON.parse(myDepartList)[0].orgCode, label: JSON.parse(myDepartList)[0].departName }] : [];

  console.log('treeData', treeData.value);
};

//部门管理员才可以编辑和删除
const isMyChargePart = (code) => {
  const myDepartList = localStorage.getItem('myResponsibleDepartList');

  const parsedDepartList = myDepartList ? JSON.parse(myDepartList) : [];
  return parsedDepartList.some((item) => item.orgCode === code);
};
onMounted(async () => {
  await setDepartmentTree();
  getList();
});
</script>

<style lang="less" scoped>
#tag-manage-wrapper {
  margin: 20px;
  padding: 10px;
  background-color: #fff;
  height: calc(100% - 40px);
  .btn-box {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    .expand-collapse {
      position: relative;
      display: flex;
      padding-left: 7px;
      font-size: 16px;
      font-weight: 500;
      line-height: 24px;
      color: rgba(0, 0, 0, 0.85);
    }
  }
  :deep(.ant-modal .ant-modal-body) {
    padding: 0px 20px 20px 20px;
  }
  :deep(.cover-modal) {
    .ant-modal-body {
      height: 300px;
      padding-top: 20px;
      display: flex;
      flex-direction: column;
      align-items: center;
    }
    .department-select {
      width: 200px;
      margin-bottom: 20px;
    }
    .avatar-uploader {
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .ant-upload-select-picture-card {
        width: 200px;
        height: 200px;
        margin-right: 0;
        margin-bottom: 0;
      }
    }
  }
}
:deep(.ant-btn-link) {
  padding: 0;
}

.report-expand-select {
  width: 180px;
  margin-right: 15px;
}
.set-cover {
  margin-right: 15px;
}
</style>
