<template>
  <a-modal :visible="props.visible" title="新增" width="950px" @ok="submit" @cancel="cancel">
    <div class="ant-modal-body">
      <a-form ref="formRef" :model="formState" :label-col="{ span: 4 }" :wrapper-col="{ span: 18 }" :rules="rules">
        <a-row>
          <a-col :span="24">
            <a-form-item label="部门名称" name="departName">
              <a-input v-model:value="formState.departName" placeholder="请输入部门名称" /> </a-form-item
          ></a-col>
        </a-row>
        <a-row v-if="isChild">
          <a-col :span="24">
            <a-form-item label="上级部门">
              <a-tree-select v-model:value="props.parentId" :tree-data="props.rootTreeData" :disabled="true" style="width: 100%">
              </a-tree-select> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="排序" name="departOrder">
              <a-input-number id="inputNumber" v-model:value="formState.departOrder" :min="0" :max="9999" placeholder="请输入排序" /> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="部门管理员" name="directorUserIds">
              <a-select
                v-model:value="directorUserIds"
                allowClear
                @click="toggleDepartAdmin"
                @change="changeDepartAdmin"
                :open="false"
                :options="ueserListOptions"
                mode="multiple"
                placeholder="请选择系统用户或创建新的管理员部门账号"
                style="width: 100%"
              >
              </a-select> </a-form-item
          ></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <div v-show="showDepartAdmin" class="departTable">
              <!-- 这里是用户表格 手写一个，框架那个改不动，形式不一样 -->
              <a-row>
                <a-form-item label="用户账号" :label-col="{ span: 8 }" :wrapper-col="{ span: 15 }">
                  <a-input v-model:value="queryParam.username" placeholder="请输入用户账号" />
                </a-form-item>
                <a-form-item label="用户名称" :label-col="{ span: 7 }" :wrapper-col="{ span: 15 }">
                  <a-input v-model:value="queryParam.realname" placeholder="请输入用户名称" />
                </a-form-item>
                <a-button type="primary" preIcon="ant-design:search-outlined" @click="searchQuery" class="PM"> 查询</a-button>
                <a-button preIcon="ant-design:reload-outlined" @click="restQuery" class="PM"> 重置</a-button>
                <a-button type="primary" preIcon="ant-design:plus-outlined" @click="handleCreate" class="PM PR"> 新增</a-button>
                <!-- <a-button type="primary" @click="handleOK" class="PM"> 确认</a-button> -->
              </a-row>
              <!--引用表格-->
              <BasicTable @register="registerTable" :rowSelection="rowSelection" @selection-change="changeSelection">
                <!--插槽:table标题-->

                <!--操作栏-->
              </BasicTable>
              <!--用户抽屉-->
              <UserDrawer @register="registerDrawer" @success="handleSuccess" />
            </div>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="部门地址" name="address">
              <a-select
                ref="mapSelect"
                v-model:value="formState.address"
                show-search
                :placeholder="isInnerNet ? '点击选择部门地址' : '请选择或输入部门地址'"
                :default-active-first-option="false"
                :show-arrow="false"
                :filter-option="false"
                :not-found-content="null"
                :allowClear="false"
                @search="handleSearch"
                @click="toggleMap"
                :disabled="isInnerNet && showMap"
              >
                <a-select-option v-for="item in options" :value="item.id" @click="currentSelect(item)">{{ item.name }}</a-select-option>
              </a-select>
              <!-- <a-input v-model:value="formState.installationSite" placeholder="请输入地址" @click="toggleMap" @change="changeVal" /> -->
              <div v-if="showMap" style="display: block; width: 100%; margin-top: 20px">
                <!-- 这里是地图组件或内容 -->
                <div id="MapContainerDevpaet"></div>
                <div class="input" style="width: 50%; position: absolute; top: 60px; left: 10px" v-if="isInnerNet">
                  <a-row>
                    <a-col :span="11" style="margin-right: 10px; height: 32px">
                      <a-form-item name="longitude" style="margin-bottom: 0">
                        <a-input v-model:value="formState.longitude" placeholder="请输入经度" @blur="validateMarker" /> </a-form-item
                    ></a-col>
                    <a-col :span="11" style="height: 32px">
                      <a-form-item name="latitude" style="margin-bottom: 0">
                        <a-input v-model:value="formState.latitude" placeholder="请输入纬度" @blur="validateMarker" /> </a-form-item
                    ></a-col>
                  </a-row>
                </div>
              </div> </a-form-item
          ></a-col>
        </a-row>
      </a-form>
    </div>
  </a-modal>
</template>
<script lang="ts" setup>
  import { saveOrUpdateDepart } from '../depart.api';
  import { listNoCareTenant, deleteUser, batchDeleteUser, getImportUrl, getExportUrl, frozenBatch, syncUser } from '@/views/system/user/user.api';
  import { useListPage } from '/@/hooks/system/useListPage';
  import { departColumns, departSearchFormSchema } from '@/views/system/user/user.data';
  import UserDrawer from '@/views/system/user/UserDrawer.vue';
  import { useDrawer } from '/@/components/Drawer';
  import { BasicTable, TableAction, ActionItem } from '/@/components/Table';
  import { reactive, ref, onMounted, nextTick, toRaw } from 'vue';
  import { UserList } from '../depart.api';
  import JSelectUser from '/@/components/Form/src/jeecg/components/JSelectUser.vue';
  import type { SelectProps } from 'ant-design-vue';
  import * as mars3d from 'mars3d';
  import * as Cesium from 'mars3d-cesium';
  import defaultMapConfig from '@/views/mapView/map3d.config.json';
  import { useBasemapConfig } from '@/hooks/web/useBasemapConfig';
  import { message } from 'ant-design-vue';
  import { getIconUrl } from '/@/utils';
  import { validatorLongitude, validatorLatitude, exceedsSixDecimalPlaces } from '/@/utils/validateLngLat';
  import { isArray } from '/@/utils/is';
  const props = defineProps({
    visible: {
      type: Boolean,
      default: false,
    },
    isChild: {
      type: Boolean,
      default: false,
    },
    parentId: {
      type: String,
      default: '',
    },
    rootTreeData: { type: Array, default: () => [] },
  });
  //注册drawer
  const [registerDrawer, { openDrawer }] = useDrawer();
  // 列表页面公共参数、方法
  const { prefixCls, tableContext } = useListPage({
    designScope: 'user-list',
    tableProps: {
      title: '用户列表',
      api: UserList,
      columns: departColumns,
      size: 'small',
      formConfig: {
        // labelWidth: 200,
        schemas: departSearchFormSchema,
      },
      showActionColumn: false,
      showTableSetting: false,
      useSearchForm: false,
      beforeFetch: (params) => {
        return Object.assign({ column: 'createTime', order: 'desc' }, params);
      },
    },
  });
  //注册table数据
  const [registerTable, { reload, setProps, updateTableDataRecord }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;
  function handleCreate() {
    openDrawer(true, {
      isUpdate: false,
      showFooter: true,
      tenantSaas: false,
    });
  }
  function handleSuccess() {
    reload();
    getUeser();
  }
  function searchQuery() {
    setProps({ searchInfo: toRaw(queryParam) });
    reload();
  }
  function restQuery() {
    queryParam.username = '';
    queryParam.realname = '';
    reload();
  }
  function changeSelection() {
    directorUserIds.value = [];
    // console.log(selectedRows.value, selectedRowKeys.value);
    selectedRows.value.forEach((item) => {
      directorUserIds.value.push(item.id);
    });
    formState.value.directorUserIds = directorUserIds.value.join(',');
  }
  function changeDepartAdmin() {
    if (directorUserIds.value.length == 0) {
      selectedRows.value = [];
      selectedRowKeys.value = [];
      // return;
    } else {
      selectedRowKeys.value = directorUserIds.value;
      selectedRows.value = selectedRows.value.filter((item) => directorUserIds.value.includes(item.id));
    }
    formState.value.directorUserIds = directorUserIds.value.join(',');
    // console.log('1111111111111111111111', formState.value.directorUserIds, selectedRows.value, selectedRowKeys.value);
  }
  let queryParam = reactive({
    username: '',
    realname: '',
  });
  let directorUserIds = ref<string[]>([]);
  const emit = defineEmits(['handleclose', 'success']);
  const { configMap } = useBasemapConfig();
  let isInnerNet = ref<boolean>(false); // 内外网标识
  const showMap = ref<boolean>(false); //地图显隐
  const showDepartAdmin = ref<boolean>(false); //用户表格显隐
  let mapSelect = ref();
  let map: any = null;
  let mapbacklayer: any = null;
  let currentMarker: any = null;
  let QueryPOI: any = null;
  let options: any = ref([]); // 搜索提示信息
  let ueserListOptions = ref<SelectProps['options']>([]);
  const jobImgUrl = (name: string, suffix = 'svg') => {
    return getIconUrl(name, 'icons/remoteOverTwo/', suffix);
  };
  let formRef = ref();
  let formState: any = ref({
    // departName: '',
    parentId: props.parentId,
    // orgCode: '',
    departOrder: 0,
    directorUserIds: '',
    // address: '',
  });
  // const validateDepartName = (_rule: any, value: string): Promise<void> => {
  //   // 调用接口查用户输入的名称是否已存在，存在则提示用户该名称已存在
  //   return new Promise((resolve, reject) => {
  //     // 编辑，input为空的时候不进行验证
  //     if (!value) {
  //       resolve();
  //     } else {
  //       deviceApi
  //         .cameraList({ deviceName: value, deviceType: '4' })
  //         .then((res: any) => {
  //           if (res.records.length > 0) {
  //             // 寻找是否有跟value相同的名称，如果有则提示用户该名称已存在
  //             const idx = res.records.findIndex((item: any) => item.deviceName === value);
  //             if (idx !== -1) {
  //               reject('该部门名称已存在，请重新输入！');
  //             } else {
  //               resolve();
  //             }
  //           } else {
  //             resolve();
  //           }
  //         })
  //         .catch((error) => {
  //           reject(`验证部门名称时出错：${error.message}`);
  //         });
  //     }
  //   });
  // };
  const rules = {
    departName: [
      { required: true, message: '部门名称不能为空', trigger: 'blur' },
      { pattern: new RegExp('^.{1,50}$'), message: '长度必须在1到50个字符之间', trigger: 'blur' },
      // { validator: validateDepartName, trigger: 'blur' },
    ],
    directorUserIds: [{ required: true, message: '部门管理员不能为空', trigger: 'change' }],
    longitude: [{ validator: validatorLongitude, trigger: 'blur' }],
    latitude: [{ validator: validatorLatitude, trigger: 'blur' }],
    address: [{ required: true, message: '安装位置不能为空', trigger: 'change' }],
  };
  // 获取用户列表焦点
  const handleFocus = () => {
    getUeser();
  };

  const getUeser = () => {
    UserList({ pageNo: 1, pageSize: 1000 }).then((res) => {
      ueserListOptions.value = [];
      console.log('oooo--999', res);
      res?.records.forEach((item: any) => {
        ueserListOptions.value?.push({ label: item.realname, value: item.id });
      });
    });
  };

  const submit = async () => {
    if (props.isChild) {
      formState.value.parentId = props.parentId;
    }
    await formRef.value.validate();

    formState.value.directorUserIds = directorUserIds.value.join(',');

    await saveOrUpdateDepart(formState.value, false);

    emit('success');
    cancel();
    console.log('formState', formState.value);
  };

  const cancel = () => {
    formRef.value.resetFields();
    formRef.value.clearValidate();
    emit('handleclose');
    formState.value = {};
    options.value = [];
    if (map) {
      map?.destroy();
      map = null;
      showMap.value = false;
    }
  };
  // 封装打点方法
  const markerFun = (longitude, latitude, tag) => {
    if (currentMarker) {
      map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
    }
    // 添加标点
    currentMarker = new mars3d.graphic.BillboardEntity({
      position: [longitude, latitude, 0], // 位置使用经纬度
      flyTo: tag, // 飞向该点
      style: {
        image: jobImgUrl('点', 'png'),
        clampToGround: true,
        horizontalOrigin: mars3d.Cesium.HorizontalOrigin.CENTER,
        verticalOrigin: mars3d.Cesium.VerticalOrigin.BOTTOM,
        pixelOffsetX: 8,
      },
    });

    map.graphicLayer.addGraphic(currentMarker);
    // 清除经纬度的校验
    if (formRef.value) {
      formRef.value.clearValidate(['longitude', 'latitude', 'address']);
    }
  };
  // 表单打点验证
  const validateMarker = () => {
    console.log(formState.value.longitude, formState.value.latitude);

    if (
      formState.value.longitude &&
      formState.value.latitude &&
      !exceedsSixDecimalPlaces(formState.value.longitude) &&
      !exceedsSixDecimalPlaces(formState.value.latitude)
    ) {
      markerFun(formState.value.longitude, formState.value.latitude, true);
      formState.value.address = formState.value.longitude + '，' + formState.value.latitude;
    } else {
      if (currentMarker) {
        map.graphicLayer.removeGraphic(currentMarker); // 移除之前的标点
      }
    }
  };
  const currentSelect = (val) => {
    formState.value.address = val.address + val.name;
    formState.value.longitude = val.lng;
    formState.value.latitude = val.lat;
    markerFun(val.lng, val.lat, true);
    options.value = [];
  };
  let timer: any = null;
  // 联网搜素
  const handleSearch = (query: string) => {
    options.value = [];
    if (timer !== null) {
      clearTimeout(timer);
    }

    timer = setTimeout(() => {
      console.log('11111111', query);
      QueryPOI?.queryText({
        text: query,
        success: function (data) {
          console.log('联网搜素', data);

          options.value = data?.list;
        },
        error: function (error) {
          console.log('联网搜素失败', error);
        },
      });
    }, 500);
  };
  // 点击地图
  const handleMapClick = (event: any) => {
    console.log('我点击地图了！', event);
    // map?.clear(true); // 清空地图上的所有标注
    // 获取点击的笛卡尔坐标
    const cartesian = event.cartesian;
    if (!cartesian) {
      console.log('无效的点击位置');
      return;
    }

    // 将笛卡尔坐标转换为经纬度
    const cartographic = Cesium.Cartographic.fromCartesian(cartesian);
    const longitude = Number(Cesium.Math.toDegrees(cartographic.longitude).toFixed(6));
    const latitude = Number(Cesium.Math.toDegrees(cartographic.latitude).toFixed(6));

    // 输出经纬度
    console.log('点击的经度:', longitude);
    console.log('点击的纬度:', latitude);
    markerFun(longitude, latitude, false);
    formState.value.longitude = longitude;
    formState.value.latitude = latitude;
    // 判断是否是内网部署
    // 不是内网
    console.log('是否内网部署', isInnerNet.value);

    if (!isInnerNet.value) {
      QueryPOI.getAddress({
        location: [longitude, latitude],
        success: function (data) {
          console.log('逆解析', data);
          formState.value.address = data.address;
        },
      });
    } else {
      formState.value.address = `${longitude}，${latitude}`;
    }
  };
  const toggleDepartAdmin = () => {
    showDepartAdmin.value = !showDepartAdmin.value;
  };
  // 初始化地图
  const toggleMap = async () => {
    options.value = [];
    if (showMap.value) {
      return;
    }

    try {
      if (map) {
        map.destroy();
        map = null;
      }

      showMap.value = true;
      await nextTick(); // 确保DOM更新完成

      const container = document.getElementById('MapContainerDevpaet');
      if (!container) {
        throw new Error('Map MapContainerDevpaet not found');
      }
      const mars3dConfig = mars3d.Util.merge(defaultMapConfig.map3d, toRaw(configMap));
      map = new mars3d.Map('MapContainerDevpaet', mars3dConfig);

      mapbacklayer = new mars3d.layer.GraphicLayer({
        allowDrillPick: true, // 如果存在坐标完全相同的图标点，可以打开该属性，click事件通过graphics判断
      });
      map.addLayer(mapbacklayer);
      map.on(mars3d.EventType.click, handleMapClick);
      // 判断是否是内网部署
      if (!isInnerNet.value) {
        QueryPOI = new mars3d.query.QueryPOI({ service: 'tdt', key: 'dc2ede760d49216f0ab31b140f032ed8' });
      }
      if (formState.value.longitude && formState.value.latitude && formState.value.address) {
        markerFun(formState.value.longitude, formState.value.latitude, true);
        map.setCameraView({ lng: formState.value.longitude, lat: formState.value.latitude, alt: 4000 }, { duration: 0.1 });
      } else {
        let latitudeLongitude = localStorage.getItem('latitudeLongitude'),
          center = latitudeLongitude ? JSON.parse(latitudeLongitude) : [116.478935, 39.997761];
        map.setCameraView({ lng: center[0], lat: center[1], alt: 4000 }, { duration: 0.1 });
      }
    } catch (error) {
      console.error('Map initialization failed:', error);
      showMap.value = false;
      message.error('地图初始化失败');
    }
  };
  onMounted(() => {
    isInnerNet.value = localStorage.getItem('environmentTag') === 'privateCloud' ? true : false;
    console.log('当前环境：', localStorage.getItem('environmentTag'));
    getUeser();
  });
  defineExpose({
    ueserListOptions,
  });
</script>
<style lang="less" scoped>
  .ant-modal-body {
    padding: 20px;
  }
  :deep(.ant-input-number) {
    width: 105px !important;
  }
  :deep(.ant-btn) {
    padding: 0;
    margin-right: 15px;
  }
  .btn {
    margin-bottom: 0px;
  }
  .departTable {
    padding: 0 60px;
    .PM {
      padding: 0 10px;
      margin-right: 10px;
    }
    // .PR {
    //   position: absolute;
    //   right: 72px;
    // }
  }
</style>
