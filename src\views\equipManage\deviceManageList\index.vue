<template>
  <div class="p-4">
    <BasicTable @register="registerTable">
      <template #tableTitle>
        <div class="queryBox">
          <div :class="queryParam.deviceType == '3' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('3')">机场 </div>
          <div :class="queryParam.deviceType == '2' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('2')">飞行器 </div>
          <div :class="queryParam.deviceType == '4' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('4')">摄像头 </div>
          <!-- <div :class="queryParam.deviceType == '5' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('5')">飞手管理 </div>
          <div :class="queryParam.deviceType == '6' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('6')">负载管理 </div>
          <div :class="queryParam.deviceType == '7' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('7')">备件管理 </div> -->
          <!-- <div :class="queryParam.deviceType == '4' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('4')">布控球
          </div>
          <div :class="queryParam.deviceType == '5' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('5')">无人车
          </div>
          <div :class="queryParam.deviceType == '6' ? 'toggleButton2' : 'toggleButton'" @click="deviceTypeChange('6')">无人船
          </div> -->
          <div class="toggleSVG" @click="handleDepartModal">
            <FileTextOutlined />
          </div>
        </div>
      </template>
      <template #toolbar>
        <div class="queryBox">
          <!-- 目前只有用户切换tab到摄像头时才会有新增 -->
          <a-button v-if="queryParam.deviceType == '2'" class="addButton" preIcon="ant-design:plus-outlined" @click="AddUavModalRef.openModal()">
            添加飞行器
          </a-button>
          <a-button v-if="queryParam.deviceType == '4'" class="addButton" preIcon="ant-design:plus-outlined" @click="handleAdd('add')">
            新增设备
          </a-button>
          <a-button v-if="queryParam.deviceType == '7'" class="addButton" preIcon="ant-design:plus-outlined" @click="handleAdd('add')">
            新增备件
          </a-button>
          <a-button v-if="queryParam.deviceType == '5'" class="addButton" preIcon="ant-design:plus-outlined" @click="handleAdd('add')">
            新增飞手
          </a-button>
          <a-button v-if="queryParam.deviceType == '6'" class="addButton" preIcon="ant-design:plus-outlined" @click="handleAdd('add')">
            新增负载
          </a-button>
        </div>
        <!-- <a-button @click="() => { trackPlaybackOpen = true }">调试</a-button> -->
        <div class="queryBox">
          <a-space>
            <a-tree-select
              ref="select"
              tree-default-expand-all
              v-model:value="sysOrgCode"
              style="width: 181px"
              :tree-data="departlistTree"
              multiple
              :treeCheckable="true"
              :treeCheckStrictly="true"
              @change="handleChangeSysOrgCode"
              placeholder="请选择部门"
              :maxTagCount="1"
              :maxTagPlaceholder="(omittedValues) => `+${omittedValues.length} 更多`"
            ></a-tree-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '4'">
          <a-space>
            <a-select
              ref="select"
              v-model:value="queryParam.producerId"
              style="width: 181px"
              :options="(mychild as any).deviceProducerOptions"
              @change="handleChangedeviceProducer"
              allowClear
              placeholder="请选择品牌"
            ></a-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '4'">
          <a-space>
            <a-select
              ref="select"
              v-model:value="queryParam.deviceModel"
              style="width: 181px"
              :options="deviceModelOptions"
              @change="handleChange"
              :placeholder="!queryParam.producerId ? '请先选择品牌' : '请选择型号'"
              allowClear
              :disabled="!queryParam.producerId"
            ></a-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '2' || queryParam.deviceType == '3' || queryParam.deviceType == '4'">
          <a-space>
            <a-select ref="select" v-model:value="queryParam.state" style="width: 181px" :options="options1" @change="handleChange"></a-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '7'">
          <a-space>
            <a-select
              ref="select"
              v-model:value="queryParam.inventoryStatus"
              style="width: 181px"
              :options="inventoryStatusOptions"
              @change="handleChange"
            ></a-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '7'">
          <a-space>
            <a-select ref="select" v-model:value="queryParam.model" style="width: 181px" :options="modelOptions" @change="handleChange"></a-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '5'">
          <a-space>
            <a-select
              ref="select"
              v-model:value="queryParam.zhizhaoleixing"
              style="width: 181px"
              :options="zhizhaoOptions"
              @change="handleChange"
            ></a-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '5'">
          <a-space>
            <a-select
              ref="select"
              v-model:value="queryParam.zhuangtai"
              style="width: 181px"
              :options="zhuangtaiOptions"
              @change="handleChange"
            ></a-select>
          </a-space>
        </div>
        <div class="queryBox" v-if="queryParam.deviceType == '6'">
          <a-space>
            <a-select
              ref="select"
              v-model:value="queryParam.shebeileixing"
              style="width: 181px"
              :options="shebeileixingOptions"
              @change="handleChange"
            ></a-select>
          </a-space>
        </div>
        <div class="queryBox">
          <a-input
            style="width: 181px"
            v-model:value="queryParam.deviceName"
            @input="InputChange"
            placeholder="请输入设备名称"
            v-if="queryParam.deviceType == '2' || queryParam.deviceType == '3' || queryParam.deviceType == '4'"
          >
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
          <a-input style="width: 181px" v-model:value="queryParam.name" @input="InputChange" placeholder="搜素" v-else>
            <template #prefix>
              <search-outlined />
            </template>
          </a-input>
        </div>
      </template>
      <template #firmwareVersion="{ record, text }">
        <div class="firmwareUpgrade">
          <span>{{ record.firmwareStatus == 1 && record.deviceType == 3 ? '升级中' : text }}</span>
          <div
            v-if="(record.firmwareStatus == 3 || record?.deviceList?.[0]?.firmwareStatus == 3) && record.deviceType == 3"
            class="upgradeButton"
            @click="upgradeButton(record)"
          >
            固件升级
          </div>
          <div v-if="record.firmwareStatus == 0 && record?.deviceList?.[0]?.firmwareStatus == 0" class="latest">最新</div>
          <div v-if="record.firmwareStatus == 1 && record.deviceType == 3" class="updateProgress">
            <Progress :percent="record.firmwareUpgradeProgress" size="small" />
          </div>
          <div class="retry" v-if="record.firmwareStatus == 2">
            <span class="text">升级失败</span>
            <a-button type="link" @click="upgradeButton(record)">重试</a-button>
          </div>
        </div>
      </template>
      <template #state="{ record, text }">
        <!-- deviceType	设备类型 3:机场,1:飞行器,2:遥控器 -->
        <!-- <a-badge :color="text == 0 ? '#f14d54' : '#87d068'" :text="text == 0 ? '离线' : '在线'" /> -->
        <!-- <a-tag :color="['f14d54','#87d068'][text]">{{ ['离线','在线'][text] }}</a-tag> -->
        <div v-if="record.deviceType == 3" class="stateBox" :class="{ stateBox2: text == 99 }">{{ dockStatusMap[text] }}</div>
        <div v-if="record.deviceType == 0" class="stateBox" :class="{ stateBox2: text == 18 }">{{
          [
            '待机',
            '起飞准备',
            '起飞准备完毕',
            '手动飞行',
            '自动起飞',
            '航线飞行',
            '全景拍照',
            '智能跟随',
            'ADS-B躲避',
            '自动返航',
            '自动降落',
            '强制降落',
            '三桨叶降落',
            '升级中',
            '未连接',
            'APAS',
            '虚拟摇杆状态',
            '指令飞行',
            '离线',
          ][text]
        }}</div>
        <div v-if="record.deviceType == 2 || record.deviceType == 4" :class="text == 0 ? 'stateBox2' : 'stateBox'">{{ ['离线', '在线'][text] }} </div>
        <!-- <a-badge v-if="record.deviceType == 0" :color="text == 0 || text == 4 ? '#87d068' : '#f14d54'"
          :text="text == 0 || text == 4 ? '在线' : '离线'" />
        <a-badge v-if="record.deviceType == 1"
          :color="text == 8 || text == 11 || text == 12 || text == 13 || text == 14 ? '#87d068' : '#f14d54'"
          :text="text == 8 || text == 11 || text == 12 || text == 13 || text == 14 ? '在线' : '离线'" />
        <a-badge v-if="record.deviceType == 2" :color="['#87d068', '#f14d54'][text]" :text="['在线', '离线'][text]" /> -->
      </template>
      <template #sysOrgCode="{ record, text }">
        {{  getDepartNameByCode(text)  }}
        <!-- {{  text  }} -->
      </template>
      <template #valueInfo="{ record, text }">
        <div v-if="record.deviceType == 4 && record.valueType == 1"> 无火灾，有火灾，火焰检测，烟雾检测，电弧检测 </div>
        <div v-else>{{ text }}</div>
      </template>
      <template #valueType="{ record, text }">
        <div v-if="record.deviceType == 4 && text == 0"> 布尔型 </div>
        <div v-if="record.deviceType == 4 && text == 1"> 枚举型 </div>
      </template>
      <!-- 备件 -->
      <template #model="{ text }">
        <div>
          {{ formDataText(text) }}
        </div>
      </template>
      <template #num="{ text }">
        <div :style="Number(text) == 0 ? 'color:#FF1818' : Number(text) > 5 ? 'color:#1DAC4C' : 'color:#ED852F'"> {{ text }} </div>
      </template>
      <!-- 负载 -->
      <template #payloadType="{ text }">
        <div>
          {{
            ['喊话器', '探照灯', '激光雷达', '红外相机', '多光谱相机', '五目相机', '可见光相机', '系留设备', '水气检测设备', '货箱与空吊设备'][text]
          }}
        </div>
      </template>
      <template #action="{ record }">
        <TableAction :actions="getTableAction(record)" :dropDownActions="getMore(record)" />
      </template>
    </BasicTable>
    <AddModal @register="registerAddModal" @success="okSuccess"></AddModal>
    <EditModal @register="registerEditModal" @success="okSuccess"></EditModal>
    <playbackModal @register="registerPlaybackModal"></playbackModal>
    <journalModal @register="registerJournalModal"></journalModal>
    <deviceLogModal @register="registerDeviceLogModal"></deviceLogModal>
    <equipModal
      :open="equipModalOpen"
      :havePlane="havePlane"
      :navEquipId="navEquipId"
      :deviceSn="deviceSn"
      :deviceId="deviceId"
      @close="
        () => {
          equipModalOpen = false;
        }
      "
    ></equipModal>
    <trackPlaybackModal
      :open="trackPlaybackOpen"
      :trackPlaybackDeviceSn="trackPlaybackDeviceSn"
      @close="
        () => {
          trackPlaybackOpen = false;
        }
      "
    ></trackPlaybackModal>
    <departModal :visible="departOpen" @closeDepart="closeDepart" :departlist="departlist"></departModal>
    <!-- 摄像头新增编辑弹窗 -->
    <DeviceModal
      ref="mychild"
      :visible="DeviceOpen"
      :deviceDialogData="deviceDialogData"
      :departlist="departlist"
      @handleclose="handleclose"
      @handleSearch="handleSearch"
    ></DeviceModal>
    <sparePartModal
      :visible="sparePartOpen"
      :sparePartDialogData="sparePartDialogData"
      @handleSearch="handleSearch"
      @handleSparePartclose="handleSparePartclose"
    ></sparePartModal>
    <payloadModal
      :visible="payloadOpen"
      :payloadDialogData="payloadDialogData"
      @handleSearch="handleSearch"
      @handlePayloadclose="handlePayloadclose"
    ></payloadModal>
    <aviatorModal
      :visible="aviatorOpen"
      :aviatorDialogData="aviatorDialogData"
      @handleSearch="handleSearch"
      @handleAviatorclose="handleAviatorclose"
    ></aviatorModal>
    <!-- 摄像头删除弹窗 -->
    <a-modal v-model:visible="visibleDelete" centered title="提示" @ok="handleOk" width="554px">
      <div class="ant-modal-body">
        <div class="left" style="margin-right: 20px; padding-top: 10px">
          <ExclamationCircleFilled style="color: #ed8a49; font-size: 36px" />
        </div>
        <div class="right" v-if="queryParam.deviceType == '4'">
          <p style="font-weight: 800; font-size: 15px; margin-bottom: 5px">确认删除"{{ visibleData.deviceName }}"？</p>
          <p>删除后该设备将从设备列表中永久删除，设备历史记录将无法查找确认删除?</p>
        </div>
        <div class="right" v-if="queryParam.deviceType == '5'">
          <p style="font-weight: 800; font-size: 15px; margin-bottom: 5px">确认删除该飞手？</p>
          <p>删除后将移除该飞手信息，确认删除？</p>
        </div>
        <div class="right" v-if="queryParam.deviceType == '6'">
          <p style="font-weight: 800; font-size: 15px; margin-bottom: 5px">确认删除该负载？</p>
          <p>删除后将移除该负载信息，确认删除？</p>
        </div>
        <div class="right" v-if="queryParam.deviceType == '7'">
          <p style="font-weight: 800; font-size: 15px; margin-bottom: 5px">确认删除该备件？</p>
          <p>删除后将移除该备件信息，确认删除？</p>
        </div>
      </div>
    </a-modal>

    <firmwareUpgradeModal @ok="searchQuery()" ref="firmwareUpgradeModalRef" />
    <AddUavModal ref="AddUavModalRef" @addOk="searchQuery()" :departlist="departlist"></AddUavModal>
  </div>
</template>
<script lang="ts" name="equipManage-deviceManageList" setup>
import { ref, watch, reactive, toRaw, computed, nextTick } from 'vue';
import { BasicTable, TableAction } from '/@/components/Table';
// import { columns } from './data';
import type { ActionItem } from '/@/components/Table';
import { useListPage } from '/@/hooks/system/useListPage';
import { SearchOutlined, ExclamationCircleFilled, FileTextOutlined } from '@ant-design/icons-vue';
import { useModal } from '/@/components/Modal';
import AddModal from './AddModal.vue';
import EditModal from './EditModal.vue';
import DeviceModal from './deviceModal.vue';
import sparePartModal from './sparePartModal.vue';
import payloadModal from './payloadModal.vue';
import playbackModal from './playbackModal.vue';
import aviatorModal from './aviatorModal.vue';
import journalModal from './journalModal.vue';
import deviceLogModal from './deviceLogModal.vue';
import equipModal from './equipModal.vue';
import trackPlaybackModal from '../../components/flightPathReplay/trackPlaybackModal.vue';
import AddUavModal from './AddUavModal.vue';
import { deviceApi } from './data.api';
import { message, Progress } from 'ant-design-vue';
import { getDepartNameByCode } from '/@/utils/common/compUtils';
import { usePermission } from '/@/hooks/web/usePermission';
import { render } from '/@/utils/common/renderUtils';
import { BasicColumn } from '/@/components/Table';
import firmwareUpgradeModal from './firmwareUpgradeModal.vue';
import departModal from './departModal.vue';
let departlist = ref<any[]>([]);
let departlistTree = ref<any[]>([]);
let sysOrgCode = ref<string[]>([]);
let recordSysOrgCode:any = ref([]);
const getdepartList = () => {
  departlist.value = [];
  departlistTree.value = [];
  // 用户所属部门列表
  const treeobj = localStorage.getItem('myDepartList');
  const treeList = treeobj && treeobj != 'null' ? JSON.parse(treeobj) : [];
  if (treeList.length > 0) {
    treeList.forEach((item: any) => {
      departlist.value?.push({ label: item.departName, value: item.orgCode });
    });
  }
  // 所在部门及其子部门树
  const treeobj2 = localStorage.getItem('myDepartAndChildrenTree');
  const treeList2 = treeobj2 && treeobj2 != 'null' ? JSON.parse(treeobj2) : [];
  if (treeList2.length > 0) {
    departlistTree.value = treeList2;
  }
  const transformTreeData = (data) => {
    return data.map((item) => {
      const transformedItem:any = {
        title: item.departName,
        key: item.orgCode,
        value: item.orgCode,
        disabled: item.disableCheckbox,
      };

      if (item.children && item.children.length > 0) {
        transformedItem.children = transformTreeData(item.children);
      }

      return transformedItem;
    });
  };

  departlistTree.value = transformTreeData(departlistTree.value);
  // console.log('所在部门树', departlistTree.value);
  // console.log('所在部门树111111111', departlist.value);

  if (departlist.value.length > 0) {
   sysOrgCode.value.push(departlist.value[0]);
   recordSysOrgCode.value = [...sysOrgCode.value];
  //  queryParam.sysOrgCode = departlist.value[0].value;
  } else {
    sysOrgCode.value = [];
    // queryParam.sysOrgCode = '';
  }
  // searchQuery();
}
getdepartList();
const firmwareUpgradeModalRef = ref(null);
const AddUavModalRef = ref(null);
const { hasPermission } = usePermission();

const isEms = localStorage.getItem('environmentTag') === 'privateCloud' ? true : false;

const dockStatusMap = {
  0: '空闲中',
  1: '现场调试',
  2: '远程调试',
  3: '固件升级中',
  4: '作业中',
  5: '待标定',
  99: '离线',
};

const columns: BasicColumn[] = [
  {
    title: '设备名称',
    dataIndex: 'deviceName',
    width: 100,
    customRender: ({ text }) => {
      return render.renderTip(text);
    },
    sorter: true,
    ifShow: (_column) => {
      if (queryParam.deviceType === '2' || queryParam.deviceType === '3' || queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '品牌',
    dataIndex: 'deviceProducer',
    width: 100,
    ifShow: (_column) => {
      if (queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '设备型号',
    dataIndex: 'deviceModel',
    width: 100,
    customRender: ({ text }) => {
      return render.renderTip(text);
    },
    ifShow: (_column) => {
      if (queryParam.deviceType === '2' || queryParam.deviceType === '3' || queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '设备SN',
    dataIndex: 'deviceSn',
    width: 100,
    customRender: ({ text }) => {
      return render.renderTip(text);
    },
    ifShow: (_column) => {
      if (queryParam.deviceType === '2' || queryParam.deviceType === '3' || queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '责任人',
    dataIndex: 'contactName',
    width: 100,
    // filters: [
    //   { text: '张三', value: '张三' },
    //   { text: '张三2', value: '张三2' }
    // ],
    customRender: ({ text }) => {
      return render.renderTip(text);
    },
    ifShow: (_column) => {
      if (queryParam.deviceType === '4' || queryParam.deviceType === '5' || queryParam.deviceType === '6' || queryParam.deviceType === '7') {
        return false;
      }
      return true;
    },
  },
  {
    title: '版本号',
    dataIndex: 'firmwareVersion',
    width: 150,
    slots: { customRender: 'firmwareVersion' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '4' || queryParam.deviceType === '5' || queryParam.deviceType === '6' || queryParam.deviceType === '7') {
        return false;
      }
      return true;
    },
  },
  {
    title: '联系方式',
    dataIndex: 'contactPhone',
    width: 100,
    customRender: ({ text }) => {
      return render.renderTip(text);
    },
    ifShow: (_column) => {
      if (queryParam.deviceType === '4' || queryParam.deviceType === '5' || queryParam.deviceType === '6' || queryParam.deviceType === '7') {
        return false;
      }
      return true;
    },
  },
  {
    title: '实时状态',
    dataIndex: 'state',
    width: 100,
    slots: { customRender: 'state' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '2' || queryParam.deviceType === '3' || queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '所属部门',
    dataIndex: 'sysOrgCode',
    width: 100,
    slots: { customRender: 'sysOrgCode' },
    // filters: [
    //   { text: '华平1', value: '华平1' },
    //   { text: '华平2', value: '华平2' }
    // ],
    customRender: ({ text }) => {
      return render.renderTip(text, 9);
    },
    // ifShow: (_column) => {
    //   if (queryParam.deviceType === '4' || queryParam.deviceType === '5' || queryParam.deviceType === '6' || queryParam.deviceType === '7') {
    //     return false;
    //   }
    //   return true;
    // },
  },
  {
    title: '最后在线定位',
    dataIndex: 'lastLocation',
    width: 100,
    customRender: ({ text, record }) => {
      if (isEms) {
        return render.renderTip(`${record.longitude}，${record.latitude}`, 9);
      } else {
        return text == '[]' ? '' : render.renderTip(text, 9);
      }
    },
    sorter: true,
    ifShow: (_column) => {
      if (queryParam.deviceType === '4' || queryParam.deviceType === '5' || queryParam.deviceType === '6' || queryParam.deviceType === '7') {
        return false;
      }
      return true;
    },
  },
  {
    title: '最后在线时间',
    dataIndex: 'loginTime',
    width: 100,
    customRender: ({ text }) => {
      return render.renderTip(text);
    },
    sorter: true,
    ifShow: (_column) => {
      if (queryParam.deviceType === '4' || queryParam.deviceType === '5' || queryParam.deviceType === '6' || queryParam.deviceType === '7') {
        return false;
      }
      return true;
    },
  },
  {
    title: '安装位置',
    dataIndex: 'location',
    width: 100,
    customRender: ({ text, record }) => {
      if (isEms) {
        return render.renderTip(`${record.longitude}，${record.latitude}`, 9);
      } else {
        return text == '[]' ? '' : render.renderTip(text, 9);
      }
    },

    ifShow: (_column) => {
      if (queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '告警名称',
    dataIndex: 'conName',
    width: 100,
    ifShow: (_column) => {
      if (queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '数据类型',
    dataIndex: 'valueType',
    width: 100,
    slots: { customRender: 'valueType' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  {
    title: '取值范围',
    dataIndex: 'valueInfo',
    width: 100,
    slots: { customRender: 'valueInfo' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '4') {
        return true;
      }
      return false;
    },
  },
  // 备件
  {
    title: '备件名称',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '7') {
        return true;
      }
      return false;
    },
  },
  {
    title: '备件类型',
    dataIndex: 'type',
    // slots: { customRender: 'type' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '7') {
        return true;
      }
      return false;
    },
  },
  {
    title: '适配机型',
    dataIndex: 'model',
    slots: { customRender: 'model' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '7') {
        return true;
      }
      return false;
    },
  },
  {
    title: '剩余库存',
    dataIndex: 'num',
    slots: { customRender: 'num' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '7') {
        return true;
      }
      return false;
    },
  },
  // 负载
  {
    title: '负载名称',
    dataIndex: 'payloadName',
    ifShow: (_column) => {
      if (queryParam.deviceType === '6') {
        return true;
      }
      return false;
    },
  },
  {
    title: '负载类型',
    dataIndex: 'payloadType',
    slots: { customRender: 'payloadType' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '6') {
        return true;
      }
      return false;
    },
  },
  {
    title: '负载品牌',
    dataIndex: 'brand',
    ifShow: (_column) => {
      if (queryParam.deviceType === '6') {
        return true;
      }
      return false;
    },
  },
  {
    title: '型号',
    dataIndex: 'model',
    ifShow: (_column) => {
      if (queryParam.deviceType === '6') {
        return true;
      }
      return false;
    },
  },
  {
    title: 'SN编号',
    dataIndex: 'deviceSN',
    ifShow: (_column) => {
      if (queryParam.deviceType === '6') {
        return true;
      }
      return false;
    },
  },
  {
    title: '适配机型',
    dataIndex: 'model',
    slots: { customRender: 'model' },
    ifShow: (_column) => {
      if (queryParam.deviceType === '6') {
        return true;
      }
      return false;
    },
  },
  {
    title: '保险期限',
    dataIndex: 'insuranceExpDate',
    ifShow: (_column) => {
      if (queryParam.deviceType === '6') {
        return true;
      }
      return false;
    },
  },
  // 飞手
  {
    title: '飞手名称',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
  {
    title: '用户名称',
    dataIndex: 'realname',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
  {
    title: '执照类型',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
  {
    title: '执照编号',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
  {
    title: '执照等级',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
  {
    title: '证书状态',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
  {
    title: '发证时间',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
  {
    title: '到期时间',
    dataIndex: 'name',
    ifShow: (_column) => {
      if (queryParam.deviceType === '5') {
        return true;
      }
      return false;
    },
  },
];
const mychild = ref(null);
let equipModalOpen = ref(false);
// const deviceName = '';

let trackPlaybackOpen = ref<boolean>(false); //轨迹回放弹窗

// let queryParam = reactive({
//   deviceName: '',
//   deviceType: '3',
//   state: '',
//   producerId: undefined,
//   deviceModel: undefined,
// });
let queryParam = reactive<{
  deviceName?: string;
  deviceType?: string; // 改为可选属性
  state?: string;
  producerId?: string | number | undefined;
  deviceModel?: string | number | undefined;
  model?: string | number | undefined; // 备件机型
  inventoryStatus?: string | number | undefined; // 备件库存状态
  name?: string | number | undefined;
  zhizhaoleixing: string | number | undefined; // 执照类型
  zhuangtai: string | number | undefined;
  shebeileixing: string | number | undefined; // 设备类型
  sysOrgCode: string | number | undefined;
  // deptQueryType: string | number | undefined; // 部门查询类型
}>({
  deviceName: '',
  deviceType: '3',
  state: '',
  producerId: undefined,
  deviceModel: undefined,
  name: undefined,
  model: '',
  inventoryStatus: '0', // 备件库存状态
  zhizhaoleixing: '',
  zhuangtai: '',
  shebeileixing: '',
  sysOrgCode: departlist.value[0].value || undefined,
  // deptQueryType: 'MULTIPLE',
});

let departOpen = ref(false);
let DeviceOpen = ref(false);
let sparePartOpen = ref(false);
let payloadOpen = ref(false);
let aviatorOpen = ref(false);
let deviceDialogData = reactive<any>({});
let sparePartDialogData = reactive<any>({});
let payloadDialogData = reactive<any>({});
let aviatorDialogData = reactive<any>({});

let deviceId = ref(); //设备ID
let navEquipId = ref(); //无人机设备ID
let deviceSn = ref(['', '']); //设备SN
let havePlane = ref<Boolean>(false); //当前机场是否有飞机

let trackPlaybackDeviceSn = ref(); // 轨迹回放设备SN

const { tableContext } = useListPage({
  designScope: 'deviceManageList',
  tableProps: {
    api: deviceApi.list,
    isTreeTable: true, // 开启树形表格
    bordered: false,
    striped: true, //斑马纹设置
    columns: columns,
    useSearchForm: false,
    // dataSource: getTreeTableData(),
    childrenColumnName: 'deviceList',
    rowKey: 'id',
    scroll: { x: 1300, y: 1000 },
    tableSetting: {
      redo: false,
      size: false,
      setting: false,
      fullScreen: false,
    },
    defaultSortFn: (sortInfo) => {
      //update-begin-author:taoyan date:2022-10-21 for: VUEN-2199【表单设计器】多字段排序
      if (sortInfo instanceof Array) {
        console.log('sortInfo', sortInfo);
      } else {
        return info || {};
      }
      //update-end-author:taoyan date:2022-10-21 for: VUEN-2199【表单设计器】多字段排序
    },
    searchInfo: {
      deviceType: queryParam.deviceType,
      sysOrgCode: queryParam.sysOrgCode,
    },
    onChange: (pagination, filters, sorter) => {
      tableSort(sorter);
    },

    // customRow: (columns, index) => {
    //   console.log(columns, index)
    // }
    // fetchSetting: {
    //   deviceName: deviceName
    // }
  },
});
const options1 = ref<SelectProps['options']>([
  {
    value: '',
    label: '全部状态',
  },
  {
    value: '1',
    label: '在线状态',
  },
  {
    value: '0',
    label: '离线状态',
  },
]);
const modelOptions = ref<SelectProps['options']>([
  {
    value: '',
    label: '全部机型',
  },
  {
    value: '0',
    label: 'DJI Matrice 3D',
  },
  {
    value: '1',
    label: 'DJI Matrice 3TD',
  },
  {
    value: '2',
    label: 'DJI Matrice 350 RTK',
  },
  {
    value: '3',
    label: 'DJI Matrice 300 RTK',
  },
  {
    value: '4',
    label: 'DJI Matrice 30',
  },
  {
    value: '5',
    label: 'DJI Matrice 30T',
  },
  {
    value: '6',
    label: 'DJI Matrice 4E',
  },
  {
    value: '7',
    label: 'DJI Matrice 4T',
  },
  {
    value: '8',
    label: 'DJI Mavic 3E',
  },
  {
    value: '9',
    label: 'DJI Mavic 3T',
  },
  {
    value: '10',
    label: '远度100V',
  },
  {
    value: '11',
    label: '远度25V',
  },
  {
    value: '12',
    label: '纵横CW-25',
  },
  {
    value: '13',
    label: '普宙S400',
  },
]);
const shebeileixingOptions = ref<SelectProps['options']>([
  {
    value: '',
    label: '全部设备类型',
  },
  {
    value: '0',
    label: '喊话器',
  },
  {
    value: '1',
    label: '探照灯',
  },
  {
    value: '2',
    label: '激光雷达',
  },
  {
    value: '3',
    label: '红外相机',
  },
  {
    value: '4',
    label: '多光谱相机',
  },
  {
    value: '5',
    label: '五目相机',
  },
  {
    value: '6',
    label: '可见光相机',
  },
  {
    value: '7',
    label: '系留设备',
  },
  {
    value: '8',
    label: '水气检测设备',
  },
  {
    value: '9',
    label: '货箱与空吊设备',
  },
]);
const inventoryStatusOptions = ref<SelectProps['options']>([
  {
    value: '0',
    label: '全部状态',
  },
  {
    value: '1',
    label: '库存正常',
  },
  {
    value: '2',
    label: '库存紧张',
  },
  {
    value: '3',
    label: '库存耗尽',
  },
]);
const zhizhaoOptions = ref<SelectProps['options']>([
  {
    value: '',
    label: '全部执照类型',
  },
  {
    value: 'CAAC',
    label: 'CAAC',
  },
  {
    value: 'AOPA',
    label: 'AOPA',
  },
  {
    value: 'ALPA',
    label: 'ALPA',
  },
]);
const zhuangtaiOptions = ref<SelectProps['options']>([
  {
    value: '',
    label: '全部状态',
  },
  {
    value: '0',
    label: '有效期内',
  },
  {
    value: '1',
    label: '即将到期(30天内)',
  },
  {
    value: '2',
    label: '已过期',
  },
]);
const [registerTable, { reload, setProps, getColumns }, { rowSelection, selectedRows, selectedRowKeys }] = tableContext;
let timer: ReturnType<typeof setTimeout>;
function InputChange(e) {
  console.log(queryParam);
  if (queryParam.deviceType == '2' || queryParam.deviceType == '3' || queryParam.deviceType == '4') {
    queryParam.deviceName = e.target.value;
  } else {
    queryParam.name = e.target.value;
  }

  if (timer) {
    clearTimeout(timer);
  }
  timer = setTimeout(() => {
    // setProps({ deviceName: e.target.value });
    searchQuery();
    // reload()
  }, 800);
}

async function deviceTypeChange(type) {
  queryParam.deviceType = await type;
  // 重置所有筛选条件
  queryParam.deviceName = '';
  queryParam.state = '';
  queryParam.producerId = undefined;
  queryParam.deviceModel = undefined;
  queryParam.name = '';
  queryParam.inventoryStatus = '0';
  queryParam.model = '';
  queryParam.zhizhaoleixing = '';
  queryParam.zhuangtai = '';
  queryParam.shebeileixing = '';

  if (type != '2' && type != '3' && type != '4') {
    delete queryParam.deviceName;
    delete queryParam.state;
  }
  if (type != '4') {
    delete queryParam.producerId;
    delete queryParam.deviceModel;
  }
  if (type != '5') {
    delete queryParam.zhizhaoleixing;
    delete queryParam.zhuangtai;
  }
  if (type != '6') {
    delete queryParam.shebeileixing;
  }
  if (type != '7') {
    delete queryParam.name;
    delete queryParam.inventoryStatus;
    delete queryParam.model;
  }
  // 切换摄像头调用这个接口
  if (type == 4) {
    setProps({ api: deviceApi.cameraList, searchInfo: toRaw(queryParam) });
  }
  // 切换机场，飞行器调用这个接口
  if (type == 2 || type == 3) {
    setProps({ api: deviceApi.list, searchInfo: toRaw(queryParam) });
  }
  if (type == 7) {
    setProps({ api: deviceApi.sparePartQuery, searchInfo: toRaw(queryParam) });
  }
  if (type == 6) {
    setProps({ api: deviceApi.payloadQuery, searchInfo: toRaw(queryParam) });
  }
  if (type == 5) {
    setProps({ api: deviceApi.aviatorQuery, searchInfo: toRaw(queryParam) });
  }
  searchQuery();
}

function searchQuery() {
  setProps({ searchInfo: toRaw(queryParam) });
  reload();
}

const [registerAddModal, { openModal: openAddModal }] = useModal();
const [registerEditModal, { openModal: openEditModal }] = useModal();
const [registerPlaybackModal, { openModal: openPlaybackModal }] = useModal();
const [registerJournalModal, { openModal: openJournalModal }] = useModal();
const [registerDeviceLogModal, { openModal: openDeviceLogModal }] = useModal();

function handleAdd(type: string, row?: any) {
  // openAddModal(true, { isUpdate: false });
  if (queryParam.deviceType == '4') {
    if (type === 'add') {
      console.log('新增');
      deviceDialogData = {};
    } else {
      console.log('编辑');
      deviceDialogData = JSON.parse(JSON.stringify(row));
    }
    DeviceOpen.value = true;
  }
  if (queryParam.deviceType == '5') {
    if (type === 'add') {
      console.log('新增');
      aviatorDialogData = {};
    } else {
      console.log('编辑');
      aviatorDialogData = JSON.parse(JSON.stringify(row));
    }
    aviatorOpen.value = true;
  }
  if (queryParam.deviceType == '6') {
    if (type === 'add') {
      console.log('新增');
      payloadDialogData = {};
    } else {
      console.log('编辑');
      payloadDialogData = JSON.parse(JSON.stringify(row));
    }
    payloadOpen.value = true;
  }
  if (queryParam.deviceType == '7') {
    if (type === 'add') {
      console.log('新增');
      sparePartDialogData = {};
    } else {
      console.log('编辑');
      sparePartDialogData = JSON.parse(JSON.stringify(row));
    }
    sparePartOpen.value = true;
  }
}
let visibleDelete = ref<boolean>(false);
let visibleData = reactive<any>({});
const handleOk = () => {
  overButton(visibleData, 'delete');
  // visibleDelete.value = false;
};
const handleSearch = () => {
  searchQuery();
  // visibleDelete.value = false;
};

function handleDepartModal() {
  departOpen.value = true;
}
function closeDepart() {
  departOpen.value = false;
}
function handleclose() {
  DeviceOpen.value = false;
}
function handleSparePartclose() {
  sparePartOpen.value = false;
}
function handlePayloadclose() {
  payloadOpen.value = false;
}
function handleAviatorclose() {
  aviatorOpen.value = false;
}

function handleChange() {
  searchQuery();
}


function handleChangeSysOrgCode(values) {
  if (sysOrgCode.value.length > 0) {
   const temp = sysOrgCode.value.map((item:any) => item.value);
   queryParam.sysOrgCode = temp.join(',');
    recordSysOrgCode.value = [...sysOrgCode.value];
  } else {
    // queryParam.sysOrgCode = '';
    sysOrgCode.value = [...recordSysOrgCode.value];
  }
  searchQuery(); 
}

const deviceModelOptions = ref<SelectProps['options']>([]);
const handleChangedeviceProducer = (value: string) => {
  if (!value) {
    queryParam.deviceModel = undefined;
  } else {
    queryParam.deviceModel = undefined;
    deviceModelOptions.value = [];
    const index = (mychild as any).value.deviceProducerList.findIndex((item) => item.id === value);
    if (index != -1) {
      (mychild as any).value.deviceProducerList[index].categoryList.forEach((item: any) => {
        deviceModelOptions.value?.push({ label: item.name, value: item.name });
      });
    }
  }
  searchQuery();
};

function overButton(record: Object, type: String) {
  console.log(record, type);
  switch (type) {
    case 'edit':
      if (record?.uavFlag == '0' && record.deviceType == '0') {
        AddUavModalRef.value.openModal(true, record);
        break;
      }
      openEditModal(true, { isUpdate: true, record });
      break;
    case 'delete':
      if (['4', '3', '2'].includes(queryParam.deviceType)) {
        getDelDevice(record);
      }
      if (queryParam.deviceType == '5') {
        aviatorDel(record);
      }
      if (queryParam.deviceType == '6') {
        payloadDel(record);
      }
      if (queryParam.deviceType == '7') {
        sparePartDel(record);
      }

      console.log('删除');
      break;
    case 'trackPlayback':
      trackPlaybackOpen.value = true;
      trackPlaybackDeviceSn.value = record.deviceSn;
      // openregistertrackPlaybackModal(true)
      break;
    case 'journal':
      openJournalModal(true, { isUpdate: true, record });
      break;
    case 'deviceLog':
      openDeviceLogModal(true, { isUpdate: true, record });
      break;
    case 'equip':
      if (record.state != 99) {
        console.log(record);
        equipModalOpen.value = true;
        deviceId.value = record.deviceId;
        navEquipId.value = record?.deviceList?.[0]?.deviceId;
        deviceSn.value[0] = record.deviceSn;
        deviceSn.value[1] = record?.deviceList?.[0]?.deviceSn;
        havePlane.value = record.deviceList ? true : false;
      } else {
        message.error('当前设备已离线!');
      }

      break;
    default:
      break;
  }
}

async function getDelDevice(record) {
  await deviceApi.del(record.id);
  visibleDelete.value = false;
  reload();
}
async function sparePartDel(record) {
  await deviceApi.sparePartDel(record.id);
  visibleDelete.value = false;
  reload();
}
async function payloadDel(record) {
  await deviceApi.payloadDel(record.id);
  visibleDelete.value = false;
  reload();
}
async function aviatorDel(record) {
  await deviceApi.aviatorDel(record.id);
  visibleDelete.value = false;
  reload();
}

function getTableAction(record): ActionItem[] {
  if (queryParam.deviceType == '4' || queryParam.deviceType == '7' || queryParam.deviceType == '6' || queryParam.deviceType == '5') {
    return [
      { label: '编辑', onClick: () => handleAdd('edit', record) },
      // { label: '回放', onClick: () => console.log('回放') },
      {
        label: '删除',
        onClick: () => {
          visibleDelete.value = true;
          visibleData = record;
        },
      },
    ];
  }
  return [
    { label: '编辑', onClick: () => overButton(record, 'edit') },
    {
      label: '删除',
      popConfirm: {
        title: '是否删除？',
        confirm: () => overButton(record, 'delete'),
      },
    },
  ];
}

function getMore(record) {
  let arr = [];

  if (record.deviceType == 3) {
    //机场才有的按钮
    //对频了的无人机的列表归属于对应的机场或遥控器之下，操作栏两行合并成一个
    arr.push(
      { label: '告警日志', onClick: () => overButton(record, 'journal') },
      {
        label: '设备调试',
        ifShow: () => {
          return hasPermission('system:user:testingEquipment');
        },
        onClick: () => overButton(record, 'equip'),
      },
      {
        label: '设备日志',
        ifShow: () => {
          // 按钮权限命名规则： 当前路由+按钮名称
          return hasPermission('equipManage:deviceManageList:deviceLog');
        },
        onClick: () => overButton(record, 'deviceLog'),
      }
    );
  }

  if (record.deviceType == 0) {
    arr.push({ label: '轨迹回放', onClick: () => overButton(record, 'trackPlayback') });
  }
  return arr;
}

function okSuccess() {
  reload();
  console.log('okSuccess');
}

function tableSort(sorter) {
  console.log(sorter);
}

function upgradeButton(record) {
  if (record.state != 0) {
    message.warn('请在机场空闲时升级固件！');
    return;
  }

  firmwareUpgradeModalRef.value.openModal(record);
}
function formDataText(text: string[] | number[]): string {
  if (!text) return '';

  const textArrData = [
    'DJI Matrice 3D',
    'DJI Matrice 3TD',
    'DJI Matrice 350 RTK',
    'DJI Matrice 300 RTK',
    'DJI Matrice 30',
    'DJI Matrice 30T',
    'DJI Matrice 4E',
    'DJI Matrice 4T',
    'DJI Mavic 3E',
    'DJI Mavic 3T',
    '远度100V',
    '远度25V',
    '纵横CW-25',
    '普宙S400',
  ];
  if (Array.isArray(text)) {
    return text
      .map((item) => {
        const index = Number(item);
        return index >= 0 && index < textArrData.length ? textArrData[index] : '';
      })
      .filter(Boolean)
      .join('/');
  } else {
    return textArrData[Number(text)];
  }
}

// function getTreeTableData() {
//   const data: any = (() => {
//     const arr: any = []
//     for (let i = 0; i < 10; i++) {
//       arr.push({
//         id: `${i}`,
//         k1: `华平测试机场${i}`,
//         k2: `DJl Dock${i}`,
//         k3: `4TAD003132131${i}`,
//         k4: `哈哈哈哈${i}`,
//         k5: `123456789${i}`,
//         k6: `0`,
//         k7: `东莞同沙水库研发部测试项目${i}`,
//         k8: `广东省深圳市龙岗区平湖街道华平科技大厦`,
//         k9: `2023-09-06 11:50:25`,
//         children: [
//           {
//             id: `12-${i}`,
//             k1: `测试机场${i}`,
//             k2: `DJl Dock${i}`,
//             k3: `4TAD003132131${i}`,
//             k4: `哈哈哈哈${i}`,
//             k5: `123456789${i}`,
//             k6: `1`,
//             k7: `东莞同沙水库研发部测试项目${i}`,
//             k8: `广东省深圳市龙岗区平湖街道华平科技大厦`,
//             k9: `2023-09-06 11:50:25`,
//           },
//           {
//             id: `13-${i}`,
//             k1: `测试机场${i}`,
//             k2: `DJl Dock${i}`,
//             k3: `4TAD003132131${i}`,
//             k4: `哈哈哈哈${i}`,
//             k5: `123456789${i}`,
//             k6: `0`,
//             k7: `东莞同沙水库研发部测试项目${i}`,
//             k8: `广东省深圳市龙岗区平湖街道华平科技大厦`,
//             k9: `2023-09-06 11:50:25`,
//           },
//           {
//             id: `14-${i}`,
//             k1: `测试机场${i}`,
//             k2: `DJl Dock${i}`,
//             k3: `4TAD003132131${i}`,
//             k4: `哈哈哈哈${i}`,
//             k5: `123456789${i}`,
//             k6: `1`,
//             k7: `东莞同沙水库研发部测试项目${i}`,
//             k8: `广东省深圳市龙岗区平湖街道华平科技大厦`,
//             k9: `2023-09-06 11:50:25`,
//           }
//         ]
//       })
//     }
//     return arr;
//   })();
//   return data

// }

</script>
<style lang="less" scoped>
.firmwareUpgrade {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 15px;
  .upgradeButton {
    font-weight: 400;
    font-size: 12px;
    color: #ffffff;
    width: 61px;
    height: 24px;
    background: #4aa1ef;
    box-shadow: 0px 2px 4px 0px rgba(0, 0, 0, 0.16);
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
  }
  .latest {
    font-weight: 400;
    font-size: 12px;
    color: #21b192;
    width: 38px;
    height: 22px;
    background: rgba(33, 177, 146, 0.1);
    border-radius: 2px;
    border: 1px solid #21b192;
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .updateProgress {
    width: 106px;
  }
  .retry {
    font-weight: 400;
    font-size: 14px;
    color: #ea2e2e;
    display: flex;
    align-items: center;
    justify-content: center;
  }
}
/deep/ .jeecg-basic-table-header__table-title-box {
  border-bottom: 1px solid #f8fafb;
}

/deep/ .ant-table-thead > tr > th {
  background: #edf6fb;
  color: #0f699a;
}

/deep/ .jeecg-basic-table-row__striped > td {
  background-color: #f7fbfc;
}

/deep/ .ant-table-tbody > tr.ant-table-row:hover > td,
.ant-table-tbody > tr > td.ant-table-cell-row-hover {
  background: #eef6fa;
}
.ant-modal-body {
  padding: 30px;
  display: flex;
}
.stateBox {
  padding: 1px 9px;
  background-color: #e7f7f1;
  color: #15b37a;
  font-size: 12px;
  display: inline-block;
}

.stateBox2 {
  padding: 1px 9px;
  background-color: #e5ecf0;
  color: #999999;
  font-size: 12px;
  display: inline-block;
}

.buttonBox {
  padding: 12px 10px 12px 10px;
  margin-bottom: 8px;
  background-color: #fff;
  border-radius: 2px;
}

.queryBox {
  margin-right: 12px;
  margin-bottom: 0px;
  .toggleSVG{
    // width: 115px;
    height: 47px;
    display: inline-block;
    font-size: 24px;
    color: #333333;
    font-weight: 700;
    cursor: pointer;
    span{
      transform: translateY(5px);
    }
    
  }

  .toggleButton {
    width: 115px;
    height: 47px;
    // background-color: red;
    display: inline-block;
    font-size: 14px;
    color: #333333;
    font-weight: 700;
    text-align: center;
    line-height: 46px;
    cursor: pointer;
  }

  .toggleButton2 {
    width: 115px;
    height: 47px;
    // background-color: red;
    display: inline-block;
    font-size: 14px;
    color: #155b81;
    font-weight: 700;
    text-align: center;
    line-height: 46px;
    border-bottom: 2px solid #155b81;
    cursor: pointer;
  }

  .addButton {
    background: linear-gradient(315deg, #1792d6 0%, #0e6ea1 100%);
    border-radius: 2px;
    color: white;
    border: none;
  }
}
</style>
